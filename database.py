"""
Trading Bot Database Module
Handles all database operations for trade logging, balance tracking, and analytics
"""

import os
import logging
import json
import hashlib
from datetime import datetime, date
from typing import Dict, List, Optional, Any, Union
from decimal import Decimal
from dataclasses import dataclass, asdict
import uuid

import psycopg2
from psycopg2.extras import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>son
from psycopg2.pool import SimpleConnectionPool
import sqlalchemy
from sqlalchemy import create_engine, text
from sqlalchemy.pool import QueuePool

logger = logging.getLogger(__name__)

@dataclass
class DatabaseConfig:
    """Database configuration"""
    host: str = "localhost"
    port: int = 5432
    database: str = "trading_bot"
    username: str = "postgres"
    password: str = ""
    pool_size: int = 5
    max_overflow: int = 10

class TradingDatabase:
    """
    Main database interface for the trading bot
    Handles connections, logging, and data retrieval
    """

    def __init__(self, config: DatabaseConfig = None):
        self.config = config or DatabaseConfig()
        self.engine = None
        self.connection_pool = None
        self.current_account_id = None
        self.current_session_id = None

        # Initialize connection
        self._initialize_connection()

        # Set up current account and session
        self._setup_default_account()

    def _initialize_connection(self):
        """Initialize database connection and pool"""
        try:
            # Create SQLAlchemy engine with connection pooling
            connection_string = (
                f"postgresql://{self.config.username}:{self.config.password}@"
                f"{self.config.host}:{self.config.port}/{self.config.database}"
            )

            self.engine = create_engine(
                connection_string,
                poolclass=QueuePool,
                pool_size=self.config.pool_size,
                max_overflow=self.config.max_overflow,
                pool_pre_ping=True,  # Verify connections before use
                echo=False  # Set to True for SQL debugging
            )

            # Test connection
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                logger.info("✅ Database connection established successfully")

        except Exception as e:
            logger.error(f"❌ Failed to connect to database: {e}")
            logger.error("Make sure PostgreSQL is running and the database exists")
            raise

    def _setup_default_account(self):
        """Set up default account and create new session"""
        try:
            # Get or create default account
            account = self.get_or_create_account(
                name="Default Testnet",
                exchange="binance",
                account_type="testnet"
            )
            self.current_account_id = account['id']

            # Create new trading session
            session = self.create_trading_session(
                strategy_type="conservative",
                symbol="BTCUSDT"
            )
            self.current_session_id = session['id']

            logger.info(f"Database initialized with account: {account['name']}, session: {session['id']}")

        except Exception as e:
            logger.error(f"Failed to setup default account: {e}")
            raise

    def execute_query(self, query: str, params: Dict = None, fetch: bool = True) -> List[Dict]:
        """Execute a SQL query with parameters"""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text(query), params or {})

                if fetch:
                    # Convert result to list of dictionaries
                    columns = result.keys()
                    rows = result.fetchall()
                    return [dict(zip(columns, row)) for row in rows]
                else:
                    conn.commit()
                    return []

        except Exception as e:
            logger.error(f"Database query failed: {e}")
            logger.error(f"Query: {query}")
            logger.error(f"Params: {params}")
            raise

    def get_or_create_account(self, name: str, exchange: str = "binance",
                            account_type: str = "testnet", api_key: str = None) -> Dict:
        """Get existing account or create new one"""

        # Hash API key for identification (don't store the actual key)
        api_key_hash = None
        if api_key:
            api_key_hash = hashlib.sha256(api_key.encode()).hexdigest()[:32]

        # Try to find existing account
        query = """
        SELECT * FROM accounts
        WHERE name = :name AND exchange = :exchange AND account_type = :account_type
        """

        accounts = self.execute_query(query, {
            'name': name,
            'exchange': exchange,
            'account_type': account_type
        })

        if accounts:
            return accounts[0]

        # Create new account
        query = """
        INSERT INTO accounts (name, exchange, account_type, api_key_hash, is_active)
        VALUES (:name, :exchange, :account_type, :api_key_hash, true)
        RETURNING *
        """

        result = self.execute_query(query, {
            'name': name,
            'exchange': exchange,
            'account_type': account_type,
            'api_key_hash': api_key_hash
        })

        logger.info(f"Created new account: {name} ({exchange} {account_type})")
        return result[0]

    def create_trading_session(self, strategy_type: str, symbol: str = "BTCUSDT",
                             use_leverage: bool = False, leverage: int = 1,
                             futures_mode: bool = False, config: Dict = None) -> Dict:
        """Create a new trading session"""

        query = """
        INSERT INTO trading_sessions (
            account_id, strategy_type, symbol, use_leverage, leverage,
            futures_mode, config_json, is_active
        )
        VALUES (
            :account_id, :strategy_type, :symbol, :use_leverage, :leverage,
            :futures_mode, :config_json, true
        )
        RETURNING *
        """

        result = self.execute_query(query, {
            'account_id': self.current_account_id,
            'strategy_type': strategy_type,
            'symbol': symbol,
            'use_leverage': use_leverage,
            'leverage': leverage,
            'futures_mode': futures_mode,
            'config_json': json.dumps(config) if config else None
        })

        logger.info(f"Created new trading session: {strategy_type} strategy for {symbol}")
        return result[0]

    def end_trading_session(self, session_id: str = None):
        """End the current or specified trading session"""
        session_id = session_id or self.current_session_id

        query = """
        UPDATE trading_sessions
        SET session_end = CURRENT_TIMESTAMP, is_active = false
        WHERE id = :session_id
        """

        self.execute_query(query, {'session_id': session_id}, fetch=False)
        logger.info(f"Ended trading session: {session_id}")

    def log_balance_snapshot(self, usdt_free: float, usdt_locked: float = 0,
                           btc_free: float = 0, btc_locked: float = 0,
                           btc_price: float = None, daily_pnl: float = 0,
                           balance_type: str = "snapshot", notes: str = None) -> str:
        """Log account balance snapshot"""

        # Calculate portfolio values
        portfolio_value_usdt = usdt_free + usdt_locked
        portfolio_value_btc = btc_free + btc_locked

        if btc_price and btc_price > 0:
            portfolio_value_usdt += (btc_free + btc_locked) * btc_price
            portfolio_value_btc += (usdt_free + usdt_locked) / btc_price

        daily_pnl_percentage = 0
        if portfolio_value_usdt > 0 and daily_pnl != 0:
            daily_pnl_percentage = (daily_pnl / portfolio_value_usdt) * 100

        query = """
        INSERT INTO account_balances (
            account_id, session_id, usdt_free, usdt_locked, btc_free, btc_locked,
            btc_price, portfolio_value_usdt, portfolio_value_btc,
            daily_pnl_usdt, daily_pnl_percentage, balance_type, notes
        )
        VALUES (
            :account_id, :session_id, :usdt_free, :usdt_locked, :btc_free, :btc_locked,
            :btc_price, :portfolio_value_usdt, :portfolio_value_btc,
            :daily_pnl_usdt, :daily_pnl_percentage, :balance_type, :notes
        )
        RETURNING id
        """

        result = self.execute_query(query, {
            'account_id': self.current_account_id,
            'session_id': self.current_session_id,
            'usdt_free': usdt_free,
            'usdt_locked': usdt_locked,
            'btc_free': btc_free,
            'btc_locked': btc_locked,
            'btc_price': btc_price,
            'portfolio_value_usdt': portfolio_value_usdt,
            'portfolio_value_btc': portfolio_value_btc,
            'daily_pnl_usdt': daily_pnl,
            'daily_pnl_percentage': daily_pnl_percentage,
            'balance_type': balance_type,
            'notes': notes
        })

        balance_id = result[0]['id']
        logger.debug(f"Logged balance snapshot: ${portfolio_value_usdt:.2f} USDT, ₿{portfolio_value_btc:.8f} BTC")
        return balance_id

    def log_trade(self, symbol: str, side: str, quantity: float, price: float,
                  order_response: Dict = None, trade_reason: str = "signal_entry",
                  signal_strength: float = None, signal_confidence: float = None,
                  stop_loss_price: float = None, take_profit_price: float = None,
                  risk_amount: float = None, leverage: int = 1, is_futures: bool = False,
                  margin_type: str = None, notes: str = None) -> str:
        """Log a completed trade"""

        # Extract data from order response if provided
        binance_order_id = None
        binance_client_order_id = None
        executed_quantity = quantity
        executed_quote_quantity = quantity * price
        commission = 0
        commission_asset = None
        status = "FILLED"
        fill_time = datetime.utcnow()

        if order_response:
            binance_order_id = order_response.get('orderId')
            binance_client_order_id = order_response.get('clientOrderId')
            executed_quantity = float(order_response.get('executedQty', quantity))
            status = order_response.get('status', 'FILLED')

            # Calculate average price from fills if available
            if order_response.get('fills'):
                total_cost = sum(float(fill['price']) * float(fill['qty']) for fill in order_response['fills'])
                total_qty = sum(float(fill['qty']) for fill in order_response['fills'])
                if total_qty > 0:
                    price = total_cost / total_qty
                    executed_quote_quantity = total_cost

                # Sum up commissions
                commission = sum(float(fill['commission']) for fill in order_response['fills'])
                if order_response['fills']:
                    commission_asset = order_response['fills'][0]['commissionAsset']

        query = """
        INSERT INTO trades (
            account_id, session_id, binance_order_id, binance_client_order_id,
            symbol, side, quantity, price, quote_quantity, executed_quantity,
            executed_quote_quantity, commission, commission_asset, trade_reason,
            signal_strength, signal_confidence, stop_loss_price, take_profit_price,
            risk_amount, leverage, is_futures, margin_type, fill_time, status,
            raw_order_response, notes
        )
        VALUES (
            :account_id, :session_id, :binance_order_id, :binance_client_order_id,
            :symbol, :side, :quantity, :price, :quote_quantity, :executed_quantity,
            :executed_quote_quantity, :commission, :commission_asset, :trade_reason,
            :signal_strength, :signal_confidence, :stop_loss_price, :take_profit_price,
            :risk_amount, :leverage, :is_futures, :margin_type, :fill_time, :status,
            :raw_order_response, :notes
        )
        RETURNING id
        """

        result = self.execute_query(query, {
            'account_id': self.current_account_id,
            'session_id': self.current_session_id,
            'binance_order_id': binance_order_id,
            'binance_client_order_id': binance_client_order_id,
            'symbol': symbol,
            'side': side,
            'quantity': quantity,
            'price': price,
            'quote_quantity': quantity * price,
            'executed_quantity': executed_quantity,
            'executed_quote_quantity': executed_quote_quantity,
            'commission': commission,
            'commission_asset': commission_asset,
            'trade_reason': trade_reason,
            'signal_strength': signal_strength,
            'signal_confidence': signal_confidence,
            'stop_loss_price': stop_loss_price,
            'take_profit_price': take_profit_price,
            'risk_amount': risk_amount,
            'leverage': leverage,
            'is_futures': is_futures,
            'margin_type': margin_type,
            'fill_time': fill_time,
            'status': status,
            'raw_order_response': json.dumps(order_response) if order_response else None,
            'notes': notes
        })

        trade_id = result[0]['id']
        logger.info(f"💾 Logged trade: {side} {executed_quantity:.8f} {symbol} @ ${price:.2f}")
        return trade_id

    def log_position_open(self, symbol: str, side: str, entry_price: float,
                         quantity: float, entry_trade_id: str, stop_loss: float = None,
                         take_profit: float = None, leverage: int = 1,
                         is_futures: bool = False, margin_type: str = None,
                         notes: str = None) -> str:
        """Log opening of a new position"""

        query = """
        INSERT INTO positions (
            account_id, session_id, symbol, side, entry_trade_id, entry_price,
            quantity, stop_loss, take_profit, leverage, is_futures, margin_type,
            status, notes
        )
        VALUES (
            :account_id, :session_id, :symbol, :side, :entry_trade_id, :entry_price,
            :quantity, :stop_loss, :take_profit, :leverage, :is_futures, :margin_type,
            'OPEN', :notes
        )
        RETURNING id
        """

        result = self.execute_query(query, {
            'account_id': self.current_account_id,
            'session_id': self.current_session_id,
            'symbol': symbol,
            'side': side,
            'entry_trade_id': entry_trade_id,
            'entry_price': entry_price,
            'quantity': quantity,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'leverage': leverage,
            'is_futures': is_futures,
            'margin_type': margin_type,
            'notes': notes
        })

        position_id = result[0]['id']
        logger.info(f"📈 Opened position: {side} {quantity:.8f} {symbol} @ ${entry_price:.2f}")
        return position_id

    def log_position_close(self, position_id: str, exit_price: float,
                          exit_trade_id: str, exit_reason: str = "manual",
                          realized_pnl: float = None, notes: str = None):
        """Log closing of a position"""

        # Calculate realized P&L if not provided
        if realized_pnl is None:
            # Get position details to calculate P&L
            position = self.get_position(position_id)
            if position:
                entry_price = float(position['entry_price'])
                quantity = float(position['quantity'])
                side = position['side']

                if side == 'LONG':
                    realized_pnl = (exit_price - entry_price) * quantity
                else:  # SHORT
                    realized_pnl = (entry_price - exit_price) * quantity

        realized_pnl_percentage = 0
        if realized_pnl and position:
            position_value = float(position['entry_price']) * float(position['quantity'])
            if position_value > 0:
                realized_pnl_percentage = (realized_pnl / position_value) * 100

        query = """
        UPDATE positions
        SET exit_trade_id = :exit_trade_id, exit_price = :exit_price,
            exit_time = CURRENT_TIMESTAMP, exit_reason = :exit_reason,
            realized_pnl = :realized_pnl, realized_pnl_percentage = :realized_pnl_percentage,
            status = 'CLOSED', notes = COALESCE(:notes, notes)
        WHERE id = :position_id
        """

        self.execute_query(query, {
            'position_id': position_id,
            'exit_trade_id': exit_trade_id,
            'exit_price': exit_price,
            'exit_reason': exit_reason,
            'realized_pnl': realized_pnl,
            'realized_pnl_percentage': realized_pnl_percentage,
            'notes': notes
        }, fetch=False)

        logger.info(f"📉 Closed position: {position_id} @ ${exit_price:.2f}, P&L: ${realized_pnl:.2f}")

    def update_position_unrealized_pnl(self, position_id: str, current_price: float,
                                     unrealized_pnl: float = None, trailing_stop: float = None):
        """Update position with current price and unrealized P&L"""

        # Calculate unrealized P&L if not provided
        if unrealized_pnl is None:
            position = self.get_position(position_id)
            if position:
                entry_price = float(position['entry_price'])
                quantity = float(position['quantity'])
                side = position['side']

                if side == 'LONG':
                    unrealized_pnl = (current_price - entry_price) * quantity
                else:  # SHORT
                    unrealized_pnl = (entry_price - current_price) * quantity

        unrealized_pnl_percentage = 0
        if unrealized_pnl and position:
            position_value = float(position['entry_price']) * float(position['quantity'])
            if position_value > 0:
                unrealized_pnl_percentage = (unrealized_pnl / position_value) * 100

        query = """
        UPDATE positions
        SET current_price = :current_price, unrealized_pnl = :unrealized_pnl,
            unrealized_pnl_percentage = :unrealized_pnl_percentage,
            trailing_stop = COALESCE(:trailing_stop, trailing_stop)
        WHERE id = :position_id AND status = 'OPEN'
        """

        self.execute_query(query, {
            'position_id': position_id,
            'current_price': current_price,
            'unrealized_pnl': unrealized_pnl,
            'unrealized_pnl_percentage': unrealized_pnl_percentage,
            'trailing_stop': trailing_stop
        }, fetch=False)

    def log_trading_signal(self, symbol: str, signal_strength: float, confidence: float,
                          price: float, action_taken: str = "HOLD", trade_id: str = None,
                          ignore_reason: str = None, strategy_type: str = None,
                          win_streak: int = 0, loss_streak: int = 0,
                          rsi: float = None, macd: float = None, macd_signal: float = None,
                          bb_position: float = None, signal_details: Dict = None) -> str:
        """Log a trading signal generated by the bot"""

        query = """
        INSERT INTO trading_signals (
            account_id, session_id, symbol, signal_strength, confidence, price,
            action_taken, trade_id, ignore_reason, strategy_type, win_streak,
            loss_streak, rsi, macd, macd_signal, bb_position, signal_details
        )
        VALUES (
            :account_id, :session_id, :symbol, :signal_strength, :confidence, :price,
            :action_taken, :trade_id, :ignore_reason, :strategy_type, :win_streak,
            :loss_streak, :rsi, :macd, :macd_signal, :bb_position, :signal_details
        )
        RETURNING id
        """

        result = self.execute_query(query, {
            'account_id': self.current_account_id,
            'session_id': self.current_session_id,
            'symbol': symbol,
            'signal_strength': signal_strength,
            'confidence': confidence,
            'price': price,
            'action_taken': action_taken,
            'trade_id': trade_id,
            'ignore_reason': ignore_reason,
            'strategy_type': strategy_type,
            'win_streak': win_streak,
            'loss_streak': loss_streak,
            'rsi': rsi,
            'macd': macd,
            'macd_signal': macd_signal,
            'bb_position': bb_position,
            'signal_details': json.dumps(signal_details) if signal_details else None
        })

        signal_id = result[0]['id']
        logger.debug(f"📊 Logged signal: {symbol} strength={signal_strength:.3f}, action={action_taken}")
        return signal_id

    def get_position(self, position_id: str) -> Optional[Dict]:
        """Get position by ID"""
        query = "SELECT * FROM positions WHERE id = :position_id"
        result = self.execute_query(query, {'position_id': position_id})
        return result[0] if result else None

    def get_open_positions(self, symbol: str = None) -> List[Dict]:
        """Get all open positions, optionally filtered by symbol"""
        query = """
        SELECT * FROM positions
        WHERE account_id = :account_id AND session_id = :session_id AND status = 'OPEN'
        """
        params = {
            'account_id': self.current_account_id,
            'session_id': self.current_session_id
        }

        if symbol:
            query += " AND symbol = :symbol"
            params['symbol'] = symbol

        query += " ORDER BY entry_time DESC"
        return self.execute_query(query, params)

    def get_recent_trades(self, limit: int = 50, symbol: str = None) -> List[Dict]:
        """Get recent trades"""
        query = """
        SELECT * FROM trades
        WHERE account_id = :account_id AND session_id = :session_id
        """
        params = {
            'account_id': self.current_account_id,
            'session_id': self.current_session_id
        }

        if symbol:
            query += " AND symbol = :symbol"
            params['symbol'] = symbol

        query += " ORDER BY order_time DESC LIMIT :limit"
        params['limit'] = limit

        return self.execute_query(query, params)

    def get_balance_history(self, days: int = 7) -> List[Dict]:
        """Get balance history for the last N days"""
        query = """
        SELECT * FROM account_balances
        WHERE account_id = :account_id AND session_id = :session_id
        AND timestamp >= CURRENT_TIMESTAMP - INTERVAL '%s days'
        ORDER BY timestamp DESC
        """ % days

        return self.execute_query(query, {
            'account_id': self.current_account_id,
            'session_id': self.current_session_id
        })

    def get_trading_performance(self, days: int = 30) -> Dict:
        """Get trading performance summary"""
        query = """
        SELECT
            COUNT(*) as total_trades,
            COUNT(CASE WHEN realized_pnl > 0 THEN 1 END) as winning_trades,
            COUNT(CASE WHEN realized_pnl < 0 THEN 1 END) as losing_trades,
            COALESCE(SUM(realized_pnl), 0) as total_pnl,
            COALESCE(AVG(realized_pnl), 0) as avg_pnl,
            COALESCE(MAX(realized_pnl), 0) as best_trade,
            COALESCE(MIN(realized_pnl), 0) as worst_trade,
            COALESCE(SUM(commission), 0) as total_fees
        FROM positions p
        LEFT JOIN trades t ON p.exit_trade_id = t.id
        WHERE p.account_id = :account_id AND p.session_id = :session_id
        AND p.status = 'CLOSED'
        AND p.exit_time >= CURRENT_TIMESTAMP - INTERVAL '%s days'
        """ % days

        result = self.execute_query(query, {
            'account_id': self.current_account_id,
            'session_id': self.current_session_id
        })

        return result[0] if result else {}

    def close(self):
        """Close database connections"""
        if self.engine:
            self.engine.dispose()
            logger.info("Database connections closed")

# Convenience functions for easy integration
def create_database_connection(config: DatabaseConfig = None) -> TradingDatabase:
    """Create a new database connection"""
    return TradingDatabase(config)

def get_default_config() -> DatabaseConfig:
    """Get default database configuration with environment variable support"""
    return DatabaseConfig(
        host=os.getenv('DB_HOST', 'localhost'),
        port=int(os.getenv('DB_PORT', '5432')),
        database=os.getenv('DB_NAME', 'trading_bot'),
        username=os.getenv('DB_USER', 'postgres'),
        password=os.getenv('DB_PASSWORD', ''),
    )