<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Advanced Trading Bot - Dashboard</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;700&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1 class="title">
                    <span class="icon">🚀</span>
                    ADVANCED TRADING BOT
                    <span class="version">v2.0</span>
                </h1>
                <div class="header-right">
                    <div class="strategy-badge" id="strategyBadge">
                        <span class="strategy-icon">🎯</span>
                        <span class="strategy-name">CONSERVATIVE</span>
                    </div>
                    <div class="status-indicator">
                        <div class="status-dot" id="statusDot"></div>
                        <span id="statusText">Disconnected</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Control Panel -->
        <section class="control-panel">
            <div class="panel-header">
                <h2>🎛️ Control Panel</h2>
            </div>
            <div class="controls">
                <button id="startBtn" class="btn btn-start">
                    <span class="btn-icon">▶️</span>
                    START BOT
                </button>
                <button id="stopBtn" class="btn btn-stop" disabled>
                    <span class="btn-icon">⏹️</span>
                    STOP BOT
                </button>
                <button id="configBtn" class="btn btn-config">
                    <span class="btn-icon">⚙️</span>
                    CONFIG
                </button>
            </div>
        </section>

        <!-- Stats Grid -->
        <section class="stats-grid">
            <!-- Account Stats -->
            <div class="stat-card">
                <div class="stat-header">
                    <h3>💰 Account Balance</h3>
                </div>
                <div class="stat-value" id="balance">$0.00</div>
                <div class="stat-change" id="balanceChange">+0.00%</div>
            </div>

            <!-- Daily P&L -->
            <div class="stat-card">
                <div class="stat-header">
                    <h3>📈 Daily P&L</h3>
                </div>
                <div class="stat-value" id="dailyPnl">$0.00</div>
                <div class="stat-change" id="pnlChange">+0.00%</div>
            </div>

            <!-- Active Positions -->
            <div class="stat-card">
                <div class="stat-header">
                    <h3>📊 Positions</h3>
                </div>
                <div class="stat-value" id="positionCount">0</div>
                <div class="stat-change">Active</div>
            </div>

            <!-- Signal Strength -->
            <div class="stat-card">
                <div class="stat-header">
                    <h3>🎯 Signal</h3>
                </div>
                <div class="stat-value" id="signalStrength">0.00</div>
                <div class="stat-change" id="signalConfidence">0% confidence</div>
            </div>

            <!-- Win Streak -->
            <div class="stat-card">
                <div class="stat-header">
                    <h3>🔥 Win Streak</h3>
                </div>
                <div class="stat-value" id="winStreak">0</div>
                <div class="stat-change" id="streakType">No trades</div>
            </div>
        </section>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Positions Panel -->
            <section class="panel positions-panel">
                <div class="panel-header">
                    <h2>📊 Active Positions</h2>
                    <div class="panel-controls">
                        <span id="totalPositionValue" class="panel-info">$0.00 total</span>
                        <button class="btn-small" id="refreshPositions">🔄</button>
                        <button class="btn-small" id="refreshPrices" title="Refresh Prices">💲</button>
                    </div>
                </div>
                <div class="panel-content">
                    <div id="positionsContainer">
                        <div class="empty-state">
                            <div class="empty-icon">📭</div>
                            <p>No active positions</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Signals Panel -->
            <section class="panel signals-panel">
                <div class="panel-header">
                    <h2>🎯 Trading Signals</h2>
                    <div class="signal-indicator" id="signalIndicator">
                        <div class="signal-dot"></div>
                        <span>Analyzing...</span>
                    </div>
                </div>
                <div class="panel-content">
                    <div id="signalsContainer">
                        <div class="empty-state">
                            <div class="empty-icon">🔍</div>
                            <p>Waiting for signals...</p>
                        </div>
                    </div>
                </div>
            </section>
        </div>

        <!-- Trade History and Activity Log -->
        <div class="bottom-panels">
            <!-- Trade History -->
            <section class="panel trade-history-panel">
                <div class="panel-header">
                    <h2>📈 Trade History</h2>
                    <div class="panel-controls">
                        <span id="tradeCount" class="panel-info">0 trades</span>
                        <button class="btn-small" id="refreshTrades">🔄</button>
                    </div>
                </div>
                <div class="panel-content">
                    <div id="tradeHistoryContainer" class="trade-history">
                        <div class="empty-state">
                            <div class="empty-icon">📊</div>
                            <p>No trades yet</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Activity Log -->
            <section class="panel activity-panel">
                <div class="panel-header">
                    <h2>📝 Activity Log</h2>
                    <div class="panel-controls">
                        <button class="btn-small" id="clearLog">🗑️</button>
                    </div>
                </div>
                <div class="panel-content">
                    <div id="activityLog" class="activity-log">
                        <div class="log-entry">
                            <span class="log-time">--:--:--</span>
                            <span class="log-message">System initialized. Ready to trade.</span>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <!-- Configuration Modal -->
    <div id="configModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>⚙️ Bot Configuration</h2>
                <button class="modal-close" id="closeModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="config-grid">
                    <div class="config-group full-width">
                        <label>Trading Strategy</label>
                        <select id="strategyType" class="strategy-selector">
                            <option value="conservative">🎯 Conservative (Quality Focused Trading)</option>
                            <option value="aggressive">🔥 Aggressive (High Frequency Trading)</option>
                            <option value="degenerate_moon_boy">🚀🌙 Degenerate Moon Boy (MAXIMUM RISK/REWARD)</option>
                        </select>
                        <div class="strategy-info">
                            <div id="conservativeInfo" class="strategy-description active">
                                <strong>🎯 Conservative Strategy:</strong> Quality-focused trading with 1.5% stops and 2:1 rewards. Trades every 15 seconds with selective signal filtering for steady growth.
                            </div>
                            <div id="aggressiveInfo" class="strategy-description">
                                <strong>🔥 Aggressive Strategy:</strong> High-frequency trading with 1.3% stops and 1.8:1 rewards. Trades every 10 seconds with sensitive signals for maximum market participation.
                            </div>
                            <div id="degenerate_moon_boyInfo" class="strategy-description">
                                <strong>🚀🌙 DEGENERATE MOON BOY:</strong> MAXIMUM RISK/REWARD! 15% risk per trade, 8:1 profit targets, 35% max daily loss. Diamond hands mode with FOMO amplification. ⚠️ FOR DEGENS ONLY! ⚠️
                            </div>
                        </div>
                    </div>
                    <div class="config-group">
                        <label>Trading Symbol</label>
                        <input type="text" id="symbol" value="BTCUSDT">
                    </div>
                    <div class="config-group">
                        <label>Base Quantity</label>
                        <input type="number" id="baseQuantity" value="0.01" step="0.001">
                    </div>
                    <div class="config-group">
                        <label>Risk Per Trade (%)</label>
                        <input type="number" id="riskPerTrade" value="2" step="0.1">
                    </div>
                    <div class="config-group">
                        <label>Max Daily Loss (%)</label>
                        <input type="number" id="maxDailyLoss" value="5" step="0.1">
                    </div>
                    <div class="config-group">
                        <label>RSI Period</label>
                        <input type="number" id="rsiPeriod" value="14" min="5" max="50">
                    </div>
                    <div class="config-group">
                        <label>RSI Oversold</label>
                        <input type="number" id="rsiOversold" value="30" min="10" max="40">
                    </div>

                    <!-- Leverage Settings Section -->
                    <div class="config-group full-width leverage-section">
                        <div class="section-header">
                            <h3>🔥 Leverage Settings</h3>
                            <div class="leverage-toggle">
                                <input type="checkbox" id="useLeverage" class="toggle-checkbox">
                                <label for="useLeverage" class="toggle-label">Enable Leverage</label>
                            </div>
                        </div>
                        <div class="leverage-warning">
                            <div class="warning-icon">⚠️</div>
                            <div class="warning-text">
                                <strong>WARNING:</strong> Leverage trading is extremely risky and can result in significant losses.
                                Only use leverage if you understand the risks and can afford to lose your entire investment.
                            </div>
                        </div>
                    </div>

                    <div id="leverageControls" class="leverage-controls" style="display: none;">
                        <div class="config-group">
                            <label>Leverage Level</label>
                            <div class="leverage-slider-container">
                                <input type="range" id="leverageSlider" min="1" max="20" value="1" class="leverage-slider">
                                <div class="leverage-display">
                                    <span id="leverageValue">1</span>x
                                    <span id="leverageRisk" class="leverage-risk-indicator">No Risk</span>
                                </div>
                            </div>
                            <div class="leverage-presets">
                                <button type="button" class="leverage-preset" data-leverage="2">2x Safe</button>
                                <button type="button" class="leverage-preset" data-leverage="5">5x Moderate</button>
                                <button type="button" class="leverage-preset" data-leverage="10">10x Risky</button>
                                <button type="button" class="leverage-preset" data-leverage="20">20x Extreme</button>
                            </div>
                        </div>

                        <div class="config-group">
                            <label>Margin Type</label>
                            <select id="marginType">
                                <option value="ISOLATED">🛡️ Isolated (Recommended)</option>
                                <option value="CROSSED">⚡ Cross Margin</option>
                            </select>
                        </div>

                        <div class="config-group">
                            <label>Trading Mode</label>
                            <select id="tradingMode">
                                <option value="spot">📊 Spot Trading</option>
                                <option value="futures">🚀 Futures Trading</option>
                            </select>
                        </div>

                        <div class="leverage-info">
                            <div class="info-item">
                                <span class="info-label">Effective Buying Power:</span>
                                <span id="effectivePower">$0.00</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Liquidation Risk:</span>
                                <span id="liquidationRisk">Low</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelConfig">Cancel</button>
                <button class="btn btn-primary" id="saveConfig">Save Configuration</button>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
</body>
</html>
