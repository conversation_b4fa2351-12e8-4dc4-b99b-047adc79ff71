#!/usr/bin/env python3
"""
Simple script to sell ETH for USDT on Binance testnet
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from binance.client import Client
    import logging
    
    # Set up logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)
    
    # API credentials (from trade-bot.py)
    API_KEY = "2Q0zD7ksWslHj6AIA3V9RlPExlgEQEIG39PS80F2DAmRRObKgLsziy1kFSBoDJEl"
    API_SECRET = "HBcDwdfWSVwS7hLAGJpRD9CzZKkZE5bAc1X5LbSskuoHx0WaNoTHivbdz2UkgQpY"
    
    def main():
        """Check balances and sell ETH for USDT"""
        try:
            # Initialize Binance client
            client = Client(API_KEY, API_SECRET, testnet=True)
            logger.info("Connected to Binance testnet")
            
            # Get account info
            account = client.get_account()
            
            # Check all balances
            logger.info("=== ACCOUNT BALANCES ===")
            balances = {}
            for balance in account['balances']:
                free = float(balance['free'])
                locked = float(balance['locked'])
                total = free + locked
                if total > 0:
                    balances[balance['asset']] = {
                        'free': free,
                        'locked': locked,
                        'total': total
                    }
                    logger.info(f"{balance['asset']}: Free={free:.8f}, Locked={locked:.8f}, Total={total:.8f}")
            
            # Check if we have ETH
            if 'ETH' not in balances or balances['ETH']['free'] <= 0:
                logger.warning("No ETH available to sell")
                
                # Show other assets we could sell
                sellable_assets = []
                for asset, balance_info in balances.items():
                    if asset not in ['USDT', 'BTC'] and balance_info['free'] > 0:
                        sellable_assets.append((asset, balance_info['free']))
                
                if sellable_assets:
                    logger.info("Other assets available to sell:")
                    for asset, amount in sellable_assets:
                        logger.info(f"  {asset}: {amount:.8f}")
                        
                        # Try to sell the first available asset
                        if len(sys.argv) > 1 and sys.argv[1] == "sell-any":
                            logger.info(f"Attempting to sell {asset}...")
                            try:
                                symbol = f"{asset}USDT"
                                order = client.order_market_sell(symbol=symbol, quantity=amount)
                                
                                if order and order.get('status') == 'FILLED':
                                    executed_qty = float(order.get('executedQty', 0))
                                    fills = order.get('fills', [])
                                    total_usdt = sum(float(fill['price']) * float(fill['qty']) for fill in fills)
                                    logger.info(f"✅ Successfully sold {executed_qty:.8f} {asset} for {total_usdt:.2f} USDT")
                                    break
                                else:
                                    logger.error(f"❌ Failed to sell {asset}: {order}")
                            except Exception as e:
                                logger.error(f"❌ Error selling {asset}: {e}")
                                continue
                return
            
            # Sell ETH
            eth_balance = balances['ETH']['free']
            logger.info(f"Found {eth_balance:.8f} ETH available to sell")
            
            # Place market sell order
            logger.info("Placing market sell order for ETH...")
            order = client.order_market_sell(symbol="ETHUSDT", quantity=eth_balance)
            
            if order and order.get('status') == 'FILLED':
                executed_qty = float(order.get('executedQty', 0))
                fills = order.get('fills', [])
                total_usdt = sum(float(fill['price']) * float(fill['qty']) for fill in fills)
                
                logger.info(f"✅ Successfully sold {executed_qty:.8f} ETH for {total_usdt:.2f} USDT")
                
                # Check new USDT balance
                new_account = client.get_account()
                for balance in new_account['balances']:
                    if balance['asset'] == 'USDT':
                        new_usdt = float(balance['free']) + float(balance['locked'])
                        logger.info(f"💰 New USDT balance: {new_usdt:.2f}")
                        break
            else:
                logger.error(f"❌ Failed to sell ETH: {order}")
                
        except Exception as e:
            logger.error(f"❌ Error: {e}")
            return False
        
        return True
    
    if __name__ == "__main__":
        success = main()
        if success:
            logger.info("🎉 ETH sale completed successfully!")
        else:
            logger.error("💥 ETH sale failed!")
            
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Try running the web GUI instead - it has the working Python environment")
    print("🌐 Go to http://localhost:6969 and use the web interface")
