#!/usr/bin/env python3
"""
Web GUI for the Advanced Trading Bot
Beautiful retro-styled interface with real-time updates
"""

from flask import Flask, render_template, jsonify, request
from flask_socketio import SocketIO, emit
import threading
import time
import json
from datetime import datetime
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import from the trade-bot.py file
import importlib.util
spec = importlib.util.spec_from_file_location("trade_bot", "trade-bot.py")
trade_bot_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(trade_bot_module)

AdvancedTradingBot = trade_bot_module.AdvancedTradingBot
TradingConfig = trade_bot_module.TradingConfig
import logging

# Configure logging for web interface
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config['SECRET_KEY'] = 'trading_bot_secret_key'
socketio = SocketIO(app, cors_allowed_origins="*")

# Global bot instance
bot = None
bot_thread = None
bot_running = False
bot_stats = {
    'status': 'Stopped',
    'balance': 0.0,
    'daily_pnl': 0.0,
    'positions': {},
    'signals': {},
    'trades_today': 0,
    'win_rate': 0.0,
    'last_update': None
}

class WebTradingBot(AdvancedTradingBot):
    """Extended trading bot with web interface integration"""

    def __init__(self, config):
        super().__init__(config)
        self.web_stats = bot_stats
        self.last_balance = 0.0
        self.balance_history = []
        self.trade_history = []



    def emit_log_message(self, message, log_type='info'):
        """Emit log message to web interface"""
        socketio.emit('log_message', {
            'message': message,
            'type': log_type,
            'timestamp': datetime.now().strftime('%H:%M:%S')
        })

    def update_web_stats(self):
        """Update statistics for web interface"""
        # Refresh prices first to ensure consistency
        self.refresh_current_prices()

        # Update position data with current prices
        self.update_position_data()

        current_balance = self.get_account_balance()

        # Track USD balance changes and detect anomalies
        if self.last_balance > 0 and abs(current_balance - self.last_balance) > 0.01:
            balance_change = current_balance - self.last_balance
            if abs(balance_change) > 1.0:  # Significant change
                self.emit_log_message(f"Balance changed by ${balance_change:.4f} (from ${self.last_balance:.2f} to ${current_balance:.2f})", 'warning')

        self.last_balance = current_balance

        # Calculate detailed position info
        detailed_positions = {}
        for symbol, pos in self.positions.items():
            current_price = self.get_current_price(symbol)
            if pos.side == 'BUY':
                unrealized_pnl = (current_price - pos.entry_price) * pos.quantity
                price_change_pct = ((current_price - pos.entry_price) / pos.entry_price) * 100
            else:
                unrealized_pnl = (pos.entry_price - current_price) * pos.quantity
                price_change_pct = ((pos.entry_price - current_price) / pos.entry_price) * 100

            # Calculate distance to stop loss and take profit
            if pos.side == 'BUY':
                stop_distance = ((current_price - pos.stop_loss) / current_price) * 100
                target_distance = ((pos.take_profit - current_price) / current_price) * 100
            else:
                stop_distance = ((pos.stop_loss - current_price) / current_price) * 100
                target_distance = ((current_price - pos.take_profit) / current_price) * 100

            # Ensure we always have valid percentages (use absolute values for display)
            stop_distance = abs(stop_distance) if stop_distance is not None else 0
            target_distance = abs(target_distance) if target_distance is not None else 0

            detailed_positions[symbol] = {
                'side': pos.side,
                'quantity': pos.quantity,
                'entry_price': pos.entry_price,
                'current_price': current_price,
                'unrealized_pnl': unrealized_pnl,
                'price_change_pct': price_change_pct,
                'stop_loss': pos.stop_loss,
                'take_profit': pos.take_profit,
                'trailing_stop': pos.trailing_stop,
                'stop_distance_pct': stop_distance,
                'target_distance_pct': target_distance,
                'entry_time': pos.entry_time.strftime('%H:%M:%S'),
                'position_size_usd': pos.quantity * current_price,
                'risk_amount': abs(pos.entry_price - pos.stop_loss) * pos.quantity
            }

        # Base stats
        stats_update = {
            'status': 'Running',
            'balance': current_balance,
            'daily_pnl': self.daily_pnl,
            'positions': detailed_positions,
            'total_positions': len(self.positions),
            'total_position_value': sum(pos['position_size_usd'] for pos in detailed_positions.values()),
            'total_unrealized_pnl': sum(pos['unrealized_pnl'] for pos in detailed_positions.values()),
            'strategy_type': self.config.strategy_type,
            'last_update': datetime.now().strftime('%H:%M:%S')
        }



        # Add performance tracking data
        stats_update.update({
            'win_streak': getattr(self, 'win_streak', 0),
            'loss_streak': getattr(self, 'loss_streak', 0),
            'recent_trades_count': len(getattr(self, 'recent_trades', []))
        })

        self.web_stats.update(stats_update)

        # Save persistent data periodically (every 30 updates to avoid excessive I/O)
        if not hasattr(self, '_stats_update_count'):
            self._stats_update_count = 0
        self._stats_update_count += 1

        if self._stats_update_count % 30 == 0:
            self.save_persistent_data()

        # Emit real-time update to web clients
        socketio.emit('stats_update', self.web_stats)
    
    def run_trading_cycle(self):
        """Override to include web updates"""
        try:
            super().run_trading_cycle()
            
            # Update web interface
            self.update_web_stats()
            
            # Get current signals for display
            signal_data = self.generate_trading_signals(self.config.symbol)
            self.web_stats['signals'] = {
                'signal': signal_data.get('signal', 0),
                'confidence': signal_data.get('confidence', 0),
                'details': signal_data.get('details', [])
            }
            
        except Exception as e:
            logger.error(f"Error in web trading cycle: {e}")
            self.web_stats['status'] = f'Error: {str(e)}'
            self.emit_log_message(f"Trading cycle error: {str(e)}", 'error')
            socketio.emit('stats_update', self.web_stats)

    def open_position(self, symbol: str, signal_data: dict) -> bool:
        """Override to add detailed logging"""
        if symbol in self.positions:
            self.emit_log_message(f"Position already exists for {symbol}", 'warning')
            return False

        # Check daily loss limit
        if self.daily_pnl <= -self.config.max_daily_loss * self.daily_start_balance:
            self.emit_log_message("Daily loss limit reached, no new positions", 'error')
            return False

        current_price = self.get_current_price(symbol)
        if current_price == 0:
            self.emit_log_message(f"Could not get price for {symbol}", 'error')
            return False

        side = 'BUY' if signal_data['signal'] > 0 else 'SELL'

        # Calculate stop loss and take profit
        stop_loss, take_profit = self.calculate_stop_loss_take_profit(symbol, current_price, side)

        # Calculate position size
        quantity = self.calculate_position_size(symbol, current_price, stop_loss)

        # Log the trade setup
        risk_amount = abs(current_price - stop_loss) * quantity
        reward_amount = abs(take_profit - current_price) * quantity
        risk_reward_ratio = reward_amount / risk_amount if risk_amount > 0 else 0

        # Strategy-specific logging
        if self.config.strategy_type == "aggressive":
            self.emit_log_message(f"🔥 AGGRESSIVE OPENING {side} POSITION", 'info')
        else:
            self.emit_log_message(f"🎯 CONSERVATIVE OPENING {side} POSITION", 'info')

        self.emit_log_message(f"Symbol: {symbol} | Price: ${current_price:.2f}", 'info')
        self.emit_log_message(f"Quantity: {quantity} | Size: ${quantity * current_price:.2f}", 'info')
        self.emit_log_message(f"Stop Loss: ${stop_loss:.2f} | Take Profit: ${take_profit:.2f}", 'info')
        self.emit_log_message(f"Risk: ${risk_amount:.2f} | Reward: ${reward_amount:.2f} | R:R = 1:{risk_reward_ratio:.1f}", 'info')
        self.emit_log_message(f"Signal Strength: {signal_data['signal']:.2f} | Confidence: {signal_data['confidence']*100:.0f}%", 'info')

        # Place the order
        order = self.place_order(symbol, side, quantity)
        if not order:
            self.emit_log_message(f"❌ Failed to place {side} order for {symbol}", 'error')
            return False

        # Create position object
        from datetime import datetime
        position = type('Position', (), {
            'symbol': symbol,
            'side': side,
            'entry_price': current_price,
            'quantity': quantity,
            'entry_time': datetime.now(),
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'trailing_stop': None,
            'unrealized_pnl': 0.0
        })()

        self.positions[symbol] = position

        # Log successful position opening
        self.emit_log_message(f"✅ {side} position opened successfully!", 'success')
        self.emit_log_message(f"Order ID: {order.get('orderId', 'N/A')}", 'info')

        # Store trade in history
        trade_record = {
            'timestamp': datetime.now().isoformat(),
            'action': 'OPEN',
            'symbol': symbol,
            'side': side,
            'quantity': quantity,
            'price': current_price,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'signal_strength': signal_data['signal'],
            'confidence': signal_data['confidence']
        }
        self.trade_history.append(trade_record)

        return True

    def close_position(self, symbol: str, reason: str = "Manual") -> bool:
        """Override to add detailed logging"""
        if symbol not in self.positions:
            self.emit_log_message(f"No position found for {symbol}", 'warning')
            return False

        position = self.positions[symbol]
        opposite_side = 'SELL' if position.side == 'BUY' else 'BUY'
        current_price = self.get_current_price(symbol)

        # Calculate PnL before closing
        if position.side == 'BUY':
            pnl = (current_price - position.entry_price) * position.quantity
            price_change_pct = ((current_price - position.entry_price) / position.entry_price) * 100
        else:
            pnl = (position.entry_price - current_price) * position.quantity
            price_change_pct = ((position.entry_price - current_price) / position.entry_price) * 100

        # Log the closing details
        self.emit_log_message(f"🔄 CLOSING {position.side} POSITION", 'info')
        self.emit_log_message(f"Symbol: {symbol} | Reason: {reason}", 'info')
        self.emit_log_message(f"Entry: ${position.entry_price:.2f} | Exit: ${current_price:.2f}", 'info')
        self.emit_log_message(f"Price Change: {price_change_pct:+.2f}%", 'info')

        order = self.place_order(symbol, opposite_side, position.quantity)
        if not order:
            self.emit_log_message(f"❌ Failed to close position for {symbol}", 'error')
            return False

        self.daily_pnl += pnl

        # Log the result
        pnl_status = "✅ PROFIT" if pnl > 0 else "❌ LOSS" if pnl < 0 else "➖ BREAKEVEN"
        self.emit_log_message(f"{pnl_status}: ${pnl:+.4f} USDT", 'success' if pnl > 0 else 'error' if pnl < 0 else 'info')
        self.emit_log_message(f"Daily P&L: ${self.daily_pnl:+.4f} USDT", 'info')

        # Store trade in history
        trade_record = {
            'timestamp': datetime.now().isoformat(),
            'action': 'CLOSE',
            'symbol': symbol,
            'side': position.side,
            'quantity': position.quantity,
            'entry_price': position.entry_price,
            'exit_price': current_price,
            'pnl': pnl,
            'reason': reason,
            'duration_minutes': (datetime.now() - position.entry_time).total_seconds() / 60
        }
        self.trade_history.append(trade_record)

        del self.positions[symbol]
        return True

def bot_worker():
    """Background worker for the trading bot"""
    global bot, bot_running
    
    try:
        while bot_running and bot:
            bot.run_trading_cycle()
            time.sleep(30)  # 30 second intervals
    except Exception as e:
        logger.error(f"Bot worker error: {e}")
        bot_stats['status'] = f'Error: {str(e)}'
        socketio.emit('stats_update', bot_stats)

@app.route('/')
def index():
    """Main dashboard page"""
    return render_template('dashboard.html')

@app.route('/api/start', methods=['POST'])
def start_bot():
    """Start the trading bot"""
    global bot, bot_thread, bot_running
    
    try:
        if bot_running:
            return jsonify({'success': False, 'message': 'Bot is already running'})
        
        # Get configuration from request
        config_data = request.get_json() or {}
        
        config = TradingConfig(
            symbol=config_data.get('symbol', 'BTCUSDT'),
            base_quantity=float(config_data.get('base_quantity', 0.01)),
            risk_per_trade=float(config_data.get('risk_per_trade', 0.02)),
            max_daily_loss=float(config_data.get('max_daily_loss', 0.05)),
            strategy_type=config_data.get('strategy_type', 'conservative'),

            # Leverage settings
            use_leverage=config_data.get('use_leverage', False),
            leverage=int(config_data.get('leverage', 1)),
            margin_type=config_data.get('margin_type', 'ISOLATED'),
            futures_mode=config_data.get('futures_mode', False),

            rsi_period=int(config_data.get('rsi_period', 14)),
            rsi_oversold=float(config_data.get('rsi_oversold', 30)),
            rsi_overbought=float(config_data.get('rsi_overbought', 70))
        )
        
        bot = WebTradingBot(config)
        bot_running = True
        bot_thread = threading.Thread(target=bot_worker, daemon=True)
        bot_thread.start()
        
        bot_stats['status'] = 'Starting...'
        return jsonify({'success': True, 'message': 'Bot started successfully'})
        
    except Exception as e:
        logger.error(f"Error starting bot: {e}")
        return jsonify({'success': False, 'message': f'Error: {str(e)}'})

@app.route('/api/stop', methods=['POST'])
def stop_bot():
    """Stop the trading bot"""
    global bot, bot_running
    
    try:
        bot_running = False
        if bot:
            # Close all positions before stopping
            bot.close_all_positions()
        
        bot_stats['status'] = 'Stopped'
        bot_stats['positions'] = {}
        socketio.emit('stats_update', bot_stats)
        
        return jsonify({'success': True, 'message': 'Bot stopped successfully'})
        
    except Exception as e:
        logger.error(f"Error stopping bot: {e}")
        return jsonify({'success': False, 'message': f'Error: {str(e)}'})

@app.route('/api/stats')
def get_stats():
    """Get current bot statistics"""
    return jsonify(bot_stats)

@app.route('/api/trades')
def get_trades():
    """Get trade history"""
    if bot and hasattr(bot, 'trade_history'):
        return jsonify({
            'trades': bot.trade_history[-50:],  # Last 50 trades
            'total_trades': len(bot.trade_history)
        })
    return jsonify({'trades': [], 'total_trades': 0})

@app.route('/api/save-data', methods=['POST'])
def save_data():
    """Manually save persistent data"""
    try:
        if bot:
            bot.save_persistent_data()
            return jsonify({'success': True, 'message': 'Data saved successfully'})
        return jsonify({'success': False, 'message': 'Bot not running'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Error saving data: {str(e)}'})

@app.route('/api/balance-history')
def get_balance_history():
    """Get balance history"""
    if bot and hasattr(bot, 'balance_history'):
        return jsonify({
            'history': bot.balance_history[-100:],  # Last 100 balance points
            'current_balance': bot.get_account_balance()
        })
    return jsonify({'history': [], 'current_balance': 0})

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    emit('stats_update', bot_stats)
    logger.info('Client connected to WebSocket')

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    logger.info('Client disconnected from WebSocket')

@app.route('/api/signals')
def get_signals():
    """Get current trading signals with advanced features"""
    if bot and hasattr(bot, 'generate_trading_signals'):
        try:
            signals = bot.generate_trading_signals(bot.config.symbol)

            # Add performance tracking to signal data
            signals.update({
                'win_streak': getattr(bot, 'win_streak', 0),
                'loss_streak': getattr(bot, 'loss_streak', 0)
            })

            return jsonify(signals)
        except Exception as e:
            logger.error(f"Error getting signals: {e}")
            return jsonify({'signal': 0, 'confidence': 0, 'error': str(e)})
    return jsonify({'signal': 0, 'confidence': 0})

@app.route('/api/positions')
def get_positions():
    """Get current positions with live data"""
    if bot:
        try:
            # Refresh prices first to ensure consistency
            bot.refresh_current_prices()

            # Update position data with current prices
            bot.update_position_data()

            # Return current positions
            positions = {}
            for symbol, position in bot.positions.items():
                current_price = bot.get_current_price(symbol)

                positions[symbol] = {
                    'symbol': symbol,
                    'side': position.side,
                    'quantity': position.quantity,
                    'entry_price': position.entry_price,
                    'current_price': current_price,
                    'unrealized_pnl': getattr(position, 'unrealized_pnl', 0),
                    'price_change_pct': getattr(position, 'price_change_pct', 0),
                    'stop_loss': position.stop_loss,
                    'take_profit': position.take_profit,
                    'trailing_stop': getattr(position, 'trailing_stop', None),
                    'entry_time': position.entry_time.strftime('%H:%M:%S') if hasattr(position.entry_time, 'strftime') else str(position.entry_time),
                    'position_size_usd': position.quantity * current_price,
                    'stop_distance_pct': abs((position.stop_loss - current_price) / current_price * 100) if position.stop_loss else 0,
                    'tp_distance_pct': abs((position.take_profit - current_price) / current_price * 100) if position.take_profit else 0,
                    'risk_amount': abs((position.entry_price - position.stop_loss) * position.quantity) if position.stop_loss else 0
                }

            return jsonify(positions)
        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return jsonify({})
    return jsonify({})



@app.route('/api/refresh-prices', methods=['POST'])
def refresh_prices():
    """Force refresh of current prices"""
    if not bot:
        return jsonify({'success': False, 'message': 'Bot not running'})

    try:
        # Clear cache and refresh prices
        bot.clear_price_cache()
        bot.refresh_current_prices()

        # Update position data with fresh prices
        bot.update_position_data()

        return jsonify({
            'success': True,
            'message': 'Prices refreshed successfully'
        })

    except Exception as e:
        logger.error(f"Error refreshing prices: {e}")
        return jsonify({'success': False, 'message': f'Error: {str(e)}'})

if __name__ == '__main__':
    print("🚀 Starting ADVANCED Trading Bot Web Interface...")
    print("📊 Dashboard will be available at: http://localhost:8080")
    print("🎯 Features: Real-time monitoring, Live P&L, Advanced AI signals")
    print("⚡ NEW: Market regime detection, adaptive signals, performance tracking")
    socketio.run(app, host='0.0.0.0', port=6969, debug=False)
