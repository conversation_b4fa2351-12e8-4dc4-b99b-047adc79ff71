#!/usr/bin/env python3
"""
Trading Bot Database Query Utility
Quick queries and analysis of your trading data
"""

import sys
from datetime import datetime, timedelta
from database import TradingDatabase, get_default_config

def print_separator(title=""):
    print("\n" + "="*60)
    if title:
        print(f" {title}")
        print("="*60)

def show_recent_trades(db, limit=10):
    """Show recent trades"""
    print_separator("RECENT TRADES")

    trades = db.get_recent_trades(limit=limit)
    if not trades:
        print("No trades found.")
        return

    print(f"{'Time':<20} {'Symbol':<10} {'Side':<5} {'Quantity':<12} {'Price':<10} {'P&L':<10}")
    print("-" * 80)

    for trade in trades:
        time_str = trade['order_time'].strftime('%Y-%m-%d %H:%M') if trade['order_time'] else 'N/A'
        symbol = trade['symbol']
        side = trade['side']
        quantity = f"{float(trade['quantity']):.6f}"
        price = f"${float(trade['price']):.2f}"

        # Calculate P&L if we have position data
        pnl = "N/A"

        print(f"{time_str:<20} {symbol:<10} {side:<5} {quantity:<12} {price:<10} {pnl:<10}")

def show_open_positions(db):
    """Show current open positions"""
    print_separator("OPEN POSITIONS")

    positions = db.get_open_positions()
    if not positions:
        print("No open positions.")
        return

    print(f"{'Symbol':<10} {'Side':<6} {'Entry Price':<12} {'Quantity':<12} {'Unrealized P&L':<15}")
    print("-" * 70)

    for pos in positions:
        symbol = pos['symbol']
        side = pos['side']
        entry_price = f"${float(pos['entry_price']):.2f}"
        quantity = f"{float(pos['quantity']):.6f}"
        unrealized_pnl = f"${float(pos['unrealized_pnl'] or 0):.2f}"

        print(f"{symbol:<10} {side:<6} {entry_price:<12} {quantity:<12} {unrealized_pnl:<15}")

def show_balance_history(db, days=7):
    """Show balance history"""
    print_separator(f"BALANCE HISTORY (Last {days} days)")

    balances = db.get_balance_history(days=days)
    if not balances:
        print("No balance history found.")
        return

    print(f"{'Time':<20} {'USDT Total':<12} {'BTC Total':<12} {'Portfolio ($)':<15} {'Daily P&L':<12}")
    print("-" * 85)

    for balance in balances[:10]:  # Show last 10 entries
        time_str = balance['timestamp'].strftime('%Y-%m-%d %H:%M') if balance['timestamp'] else 'N/A'
        usdt_total = f"${float(balance['usdt_total'] or 0):.2f}"
        btc_total = f"₿{float(balance['btc_total'] or 0):.6f}"
        portfolio = f"${float(balance['portfolio_value_usdt'] or 0):.2f}"
        daily_pnl = f"${float(balance['daily_pnl_usdt'] or 0):.2f}"

        print(f"{time_str:<20} {usdt_total:<12} {btc_total:<12} {portfolio:<15} {daily_pnl:<12}")

def show_performance_summary(db, days=30):
    """Show trading performance summary"""
    print_separator(f"PERFORMANCE SUMMARY (Last {days} days)")

    performance = db.get_trading_performance(days=days)
    if not performance or performance.get('total_trades', 0) == 0:
        print("No trading performance data available.")
        return

    total_trades = int(performance.get('total_trades', 0))
    winning_trades = int(performance.get('winning_trades', 0))
    losing_trades = int(performance.get('losing_trades', 0))
    total_pnl = float(performance.get('total_pnl', 0))
    avg_pnl = float(performance.get('avg_pnl', 0))
    best_trade = float(performance.get('best_trade', 0))
    worst_trade = float(performance.get('worst_trade', 0))
    total_fees = float(performance.get('total_fees', 0))

    win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0

    print(f"Total Trades:     {total_trades}")
    print(f"Winning Trades:   {winning_trades}")
    print(f"Losing Trades:    {losing_trades}")
    print(f"Win Rate:         {win_rate:.1f}%")
    print(f"Total P&L:        ${total_pnl:.2f}")
    print(f"Average P&L:      ${avg_pnl:.2f}")
    print(f"Best Trade:       ${best_trade:.2f}")
    print(f"Worst Trade:      ${worst_trade:.2f}")
    print(f"Total Fees:       ${total_fees:.2f}")

def show_signal_analysis(db, days=7):
    """Show recent trading signals"""
    print_separator(f"RECENT SIGNALS (Last {days} days)")

    query = """
    SELECT timestamp, symbol, signal_strength, confidence, action_taken, price, rsi, macd
    FROM trading_signals
    WHERE timestamp >= CURRENT_TIMESTAMP - INTERVAL '%s days'
    ORDER BY timestamp DESC
    LIMIT 20
    """ % days

    signals = db.execute_query(query)
    if not signals:
        print("No signals found.")
        return

    print(f"{'Time':<20} {'Symbol':<8} {'Strength':<10} {'Confidence':<12} {'Action':<8} {'Price':<10}")
    print("-" * 80)

    for signal in signals:
        time_str = signal['timestamp'].strftime('%m-%d %H:%M') if signal['timestamp'] else 'N/A'
        symbol = signal['symbol']
        strength = f"{float(signal['signal_strength'] or 0):.3f}"
        confidence = f"{float(signal['confidence'] or 0):.3f}"
        action = signal['action_taken'] or 'HOLD'
        price = f"${float(signal['price'] or 0):.0f}"

        print(f"{time_str:<20} {symbol:<8} {strength:<10} {confidence:<12} {action:<8} {price:<10}")

def main():
    """Main query interface"""
    try:
        # Connect to database
        config = get_default_config()
        db = TradingDatabase(config)

        print("🗄️ Trading Bot Database Query Tool")
        print(f"Connected to: {config.username}@{config.host}:{config.port}/{config.database}")

        if len(sys.argv) > 1:
            command = sys.argv[1].lower()

            if command == "trades":
                limit = int(sys.argv[2]) if len(sys.argv) > 2 else 10
                show_recent_trades(db, limit)
            elif command == "positions":
                show_open_positions(db)
            elif command == "balance":
                days = int(sys.argv[2]) if len(sys.argv) > 2 else 7
                show_balance_history(db, days)
            elif command == "performance":
                days = int(sys.argv[2]) if len(sys.argv) > 2 else 30
                show_performance_summary(db, days)
            elif command == "signals":
                days = int(sys.argv[2]) if len(sys.argv) > 2 else 7
                show_signal_analysis(db, days)
            else:
                print(f"Unknown command: {command}")
                print_usage()
        else:
            # Show everything by default
            show_performance_summary(db)
            show_open_positions(db)
            show_recent_trades(db, 5)
            show_balance_history(db, 3)
            show_signal_analysis(db, 3)

        db.close()

    except Exception as e:
        print(f"❌ Error: {e}")
        print("Make sure PostgreSQL is running and the database is initialized.")
        sys.exit(1)

def print_usage():
    """Print usage information"""
    print("\nUsage:")
    print("  python query_database.py                    # Show summary")
    print("  python query_database.py trades [limit]     # Show recent trades")
    print("  python query_database.py positions          # Show open positions")
    print("  python query_database.py balance [days]     # Show balance history")
    print("  python query_database.py performance [days] # Show performance summary")
    print("  python query_database.py signals [days]     # Show recent signals")

if __name__ == "__main__":
    main()