# 🔥 Leverage Trading Guide

## ⚠️ **CRITICAL WARNING** ⚠️

**Leverage trading is extremely risky and can result in significant losses, including losing more than your initial investment. Only trade with money you can afford to lose completely.**

## What is Leverage Trading?

Leverage allows you to control larger positions with less capital by borrowing funds from the exchange. For example:

- **2x Leverage**: Control $200 worth of crypto with $100 of your own money
- **5x Leverage**: Control $500 worth of crypto with $100 of your own money  
- **10x Leverage**: Control $1000 worth of crypto with $100 of your own money

## How It Works in the Bot

The bot now supports leverage trading through Binance Futures with the following features:

### Configuration Options

```python
config = TradingConfig(
    # Enable leverage
    use_leverage=True,          # Enable/disable leverage
    leverage=2,                 # Leverage multiplier (1x-125x)
    margin_type="ISOLATED",     # "ISOLATED" or "CROSSED"
    futures_mode=True,          # Must be True for leverage
    
    # Recommended safety settings
    risk_per_trade=0.01,        # Lower risk per trade
    max_daily_loss=0.03,        # Lower daily loss limit
    stop_loss_atr_multiplier=1.0, # Tighter stops
)
```

### Margin Types

1. **ISOLATED Margin** (Recommended)
   - Risk is limited to the margin allocated to each position
   - Safer option as losses can't exceed position margin
   - Each position is independent

2. **CROSSED Margin**
   - Uses entire account balance as margin
   - Higher risk as entire balance can be lost
   - Not recommended for beginners

## Risk Management with Leverage

### 1. Position Sizing
- Use smaller position sizes with leverage
- The bot automatically calculates effective exposure
- Example: $100 position with 5x leverage = $500 market exposure

### 2. Stop Losses
- Leverage amplifies losses, so tight stop losses are critical
- The bot uses tighter stop losses when leverage is enabled
- Consider using trailing stops to lock in profits

### 3. Daily Loss Limits
- Set lower daily loss limits when using leverage
- Recommended: 2-3% daily loss limit with leverage vs 5% without

## Leverage Levels and Risk

| Leverage | Risk Level | Description |
|----------|------------|-------------|
| 1x | None | No leverage (spot trading) |
| 2x-3x | Low | Conservative leverage, good for beginners |
| 5x-10x | Medium | Moderate risk, requires experience |
| 20x-50x | High | High risk, for experienced traders only |
| 100x+ | Extreme | Extremely risky, can be liquidated quickly |

## Getting Started with Leverage

### Step 1: Enable Leverage in Configuration

```python
# In trade-bot.py main() function, update the config:
config = TradingConfig(
    # ... other settings ...
    
    # Enable leverage (start conservative!)
    use_leverage=True,
    leverage=2,  # Start with 2x leverage
    margin_type="ISOLATED",
    futures_mode=True,
    
    # Use conservative risk settings
    risk_per_trade=0.01,  # 1% risk per trade
    max_daily_loss=0.03,  # 3% max daily loss
)
```

### Step 2: Test on Testnet First

The bot uses Binance testnet by default. Test your leverage settings thoroughly before using real money.

### Step 3: Start Small

- Begin with 2x leverage
- Use small position sizes
- Monitor closely for the first few trades

## Example Configurations

### Conservative 2x Leverage
```python
config = TradingConfig(
    use_leverage=True,
    leverage=2,
    margin_type="ISOLATED",
    futures_mode=True,
    risk_per_trade=0.01,
    max_daily_loss=0.03,
    strategy_type="conservative"
)
```

### Moderate 5x Leverage
```python
config = TradingConfig(
    use_leverage=True,
    leverage=5,
    margin_type="ISOLATED", 
    futures_mode=True,
    risk_per_trade=0.008,  # Even lower risk
    max_daily_loss=0.02,   # Even lower daily limit
    strategy_type="conservative"
)
```

## Using the Leverage Example Script

Run the included example script to try different leverage levels:

```bash
# Show leverage information
python leverage_example.py info

# Run with conservative 2x leverage
python leverage_example.py conservative

# Run with moderate 5x leverage  
python leverage_example.py moderate

# Run with aggressive 10x leverage (RISKY!)
python leverage_example.py aggressive
```

## Web GUI Support

The web interface now includes leverage controls:

1. **Enable Leverage**: Checkbox to enable/disable leverage
2. **Leverage Level**: Slider to set leverage (1x-20x)
3. **Margin Type**: Dropdown to select ISOLATED or CROSSED
4. **Futures Mode**: Checkbox to enable futures trading

## Monitoring Leveraged Positions

The bot provides enhanced logging for leveraged positions:

```
🔥 LEVERAGE ENABLED: 5x leverage, ISOLATED margin
📈 FUTURES MODE: Using futures trading with leverage
Position value: 100.00 USDT (🔥 500.00 USDT with 5x leverage)
🔥 Base PnL: $10.00, Leveraged PnL: $50.00 (5x)
```

## Safety Checklist

Before using leverage:

- [ ] Tested thoroughly on testnet
- [ ] Started with low leverage (2x-3x)
- [ ] Set appropriate risk limits
- [ ] Using isolated margin
- [ ] Have stop losses configured
- [ ] Only using money you can afford to lose
- [ ] Understand liquidation risks
- [ ] Plan to monitor positions closely

## Common Mistakes to Avoid

1. **Starting with high leverage** - Begin with 2x-3x
2. **Using cross margin** - Stick with isolated margin
3. **Ignoring stop losses** - Always use stop losses
4. **Overleveraging** - Don't use maximum leverage
5. **Not monitoring positions** - Watch your trades closely
6. **FOMO trading** - Stick to your strategy
7. **Revenge trading** - Don't chase losses

## Emergency Procedures

If things go wrong:

1. **Stop the bot immediately**
2. **Close all positions manually** if needed
3. **Review what happened** before restarting
4. **Reduce leverage** for future trades
5. **Consider taking a break** to reassess

## Conclusion

Leverage trading can be profitable but is inherently risky. Start small, use proper risk management, and never trade with money you can't afford to lose. The bot's leverage features are designed to help manage risk, but ultimately the responsibility lies with you.

**Remember: The goal is consistent, sustainable profits, not quick riches.**
