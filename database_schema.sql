-- Trading Bot Database Schema
-- PostgreSQL database schema for comprehensive trade logging and analytics

-- Create database (run this manually if needed)
-- CREATE DATABASE trading_bot;

-- Use the trading_bot database
-- \c trading_bot;

-- Enable UUID extension for unique identifiers
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Table: accounts
-- Tracks different trading accounts and their configurations
CREATE TABLE IF NOT EXISTS accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    exchange VARCHAR(50) NOT NULL DEFAULT 'binance',
    account_type VARCHAR(20) NOT NULL DEFAULT 'testnet', -- 'testnet', 'live'
    api_key_hash VARCHAR(255), -- Store hash of API key for identification
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table: trading_sessions
-- Tracks bot trading sessions and configurations
CREATE TABLE IF NOT EXISTS trading_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
    strategy_type VARCHAR(50) NOT NULL, -- 'conservative', 'aggressive', 'degenerate_moon_boy'
    symbol VARCHAR(20) NOT NULL DEFAULT 'BTCUSDT',
    use_leverage BOOLEAN DEFAULT false,
    leverage INTEGER DEFAULT 1,
    futures_mode BOOLEAN DEFAULT false,
    session_start TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    session_end TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    config_json JSONB, -- Store full configuration as JSON
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table: account_balances
-- Tracks account balance snapshots over time
CREATE TABLE IF NOT EXISTS account_balances (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
    session_id UUID REFERENCES trading_sessions(id) ON DELETE CASCADE,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- USDT balances
    usdt_free DECIMAL(18, 8) DEFAULT 0,
    usdt_locked DECIMAL(18, 8) DEFAULT 0,
    usdt_total DECIMAL(18, 8) GENERATED ALWAYS AS (usdt_free + usdt_locked) STORED,

    -- BTC balances
    btc_free DECIMAL(18, 8) DEFAULT 0,
    btc_locked DECIMAL(18, 8) DEFAULT 0,
    btc_total DECIMAL(18, 8) GENERATED ALWAYS AS (btc_free + btc_locked) STORED,

    -- Portfolio values
    btc_price DECIMAL(18, 2), -- BTC price at time of snapshot
    portfolio_value_usdt DECIMAL(18, 8), -- Total portfolio value in USDT
    portfolio_value_btc DECIMAL(18, 8), -- Total portfolio value in BTC

    -- Daily tracking
    daily_pnl_usdt DECIMAL(18, 8) DEFAULT 0,
    daily_pnl_percentage DECIMAL(8, 4) DEFAULT 0,

    -- Metadata
    balance_type VARCHAR(20) DEFAULT 'snapshot', -- 'snapshot', 'trade_pre', 'trade_post'
    notes TEXT
);

-- Table: trades
-- Comprehensive trade logging
CREATE TABLE IF NOT EXISTS trades (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
    session_id UUID REFERENCES trading_sessions(id) ON DELETE CASCADE,

    -- Trade identification
    binance_order_id BIGINT,
    binance_client_order_id VARCHAR(100),

    -- Trade details
    symbol VARCHAR(20) NOT NULL,
    side VARCHAR(10) NOT NULL, -- 'BUY', 'SELL'
    order_type VARCHAR(20) DEFAULT 'MARKET', -- 'MARKET', 'LIMIT', 'STOP_LOSS', etc.

    -- Quantities and prices
    quantity DECIMAL(18, 8) NOT NULL,
    price DECIMAL(18, 8) NOT NULL,
    quote_quantity DECIMAL(18, 8), -- USDT amount

    -- Execution details
    executed_quantity DECIMAL(18, 8),
    executed_quote_quantity DECIMAL(18, 8),
    commission DECIMAL(18, 8) DEFAULT 0,
    commission_asset VARCHAR(10),

    -- Trade context
    trade_reason VARCHAR(100), -- 'signal_entry', 'stop_loss', 'take_profit', 'manual'
    signal_strength DECIMAL(8, 4),
    signal_confidence DECIMAL(8, 4),

    -- Risk management
    stop_loss_price DECIMAL(18, 8),
    take_profit_price DECIMAL(18, 8),
    risk_amount DECIMAL(18, 8),

    -- Leverage info
    leverage INTEGER DEFAULT 1,
    is_futures BOOLEAN DEFAULT false,
    margin_type VARCHAR(20), -- 'ISOLATED', 'CROSSED'

    -- Timestamps
    order_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    fill_time TIMESTAMP WITH TIME ZONE,

    -- Status
    status VARCHAR(20) DEFAULT 'NEW', -- 'NEW', 'FILLED', 'CANCELLED', 'REJECTED'

    -- Metadata
    raw_order_response JSONB, -- Store full Binance response
    notes TEXT
);

-- Table: positions
-- Track open positions and their lifecycle
CREATE TABLE IF NOT EXISTS positions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
    session_id UUID REFERENCES trading_sessions(id) ON DELETE CASCADE,

    -- Position identification
    symbol VARCHAR(20) NOT NULL,
    side VARCHAR(10) NOT NULL, -- 'LONG', 'SHORT'

    -- Entry details
    entry_trade_id UUID REFERENCES trades(id),
    entry_price DECIMAL(18, 8) NOT NULL,
    entry_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    quantity DECIMAL(18, 8) NOT NULL,

    -- Risk management
    stop_loss DECIMAL(18, 8),
    take_profit DECIMAL(18, 8),
    trailing_stop DECIMAL(18, 8),

    -- Current status
    current_price DECIMAL(18, 8),
    unrealized_pnl DECIMAL(18, 8) DEFAULT 0,
    unrealized_pnl_percentage DECIMAL(8, 4) DEFAULT 0,

    -- Exit details (when closed)
    exit_trade_id UUID REFERENCES trades(id),
    exit_price DECIMAL(18, 8),
    exit_time TIMESTAMP WITH TIME ZONE,
    exit_reason VARCHAR(50), -- 'take_profit', 'stop_loss', 'manual', 'trailing_stop'

    -- Final P&L (when closed)
    realized_pnl DECIMAL(18, 8),
    realized_pnl_percentage DECIMAL(8, 4),

    -- Leverage info
    leverage INTEGER DEFAULT 1,
    is_futures BOOLEAN DEFAULT false,
    margin_type VARCHAR(20),

    -- Status
    status VARCHAR(20) DEFAULT 'OPEN', -- 'OPEN', 'CLOSED', 'CANCELLED'

    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    notes TEXT
);

-- Table: trading_signals
-- Log all trading signals generated by the bot
CREATE TABLE IF NOT EXISTS trading_signals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
    session_id UUID REFERENCES trading_sessions(id) ON DELETE CASCADE,

    -- Signal details
    symbol VARCHAR(20) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    signal_strength DECIMAL(8, 4) NOT NULL, -- -2.0 to **** (or higher for degenerate mode)
    confidence DECIMAL(8, 4) NOT NULL, -- 0.0 to 1.0

    -- Market data at signal time
    price DECIMAL(18, 8) NOT NULL,
    rsi DECIMAL(8, 4),
    macd DECIMAL(8, 4),
    macd_signal DECIMAL(8, 4),
    bb_position DECIMAL(8, 4), -- Position relative to Bollinger Bands

    -- Signal components
    signal_details JSONB, -- Store detailed signal breakdown

    -- Action taken
    action_taken VARCHAR(20), -- 'BUY', 'SELL', 'HOLD', 'IGNORED'
    trade_id UUID REFERENCES trades(id), -- If a trade was executed
    ignore_reason VARCHAR(100), -- Why signal was ignored

    -- Strategy context
    strategy_type VARCHAR(50),
    win_streak INTEGER DEFAULT 0,
    loss_streak INTEGER DEFAULT 0
);

-- Table: performance_metrics
-- Daily/periodic performance summaries
CREATE TABLE IF NOT EXISTS performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID REFERENCES accounts(id) ON DELETE CASCADE,
    session_id UUID REFERENCES trading_sessions(id) ON DELETE CASCADE,

    -- Time period
    date DATE NOT NULL,
    period_type VARCHAR(20) DEFAULT 'daily', -- 'daily', 'weekly', 'monthly'

    -- Trading activity
    total_trades INTEGER DEFAULT 0,
    winning_trades INTEGER DEFAULT 0,
    losing_trades INTEGER DEFAULT 0,
    win_rate DECIMAL(8, 4) GENERATED ALWAYS AS (
        CASE WHEN total_trades > 0 THEN (winning_trades::DECIMAL / total_trades) * 100 ELSE 0 END
    ) STORED,

    -- P&L metrics
    gross_profit DECIMAL(18, 8) DEFAULT 0,
    gross_loss DECIMAL(18, 8) DEFAULT 0,
    net_pnl DECIMAL(18, 8) DEFAULT 0,
    net_pnl_percentage DECIMAL(8, 4) DEFAULT 0,

    -- Portfolio metrics
    starting_balance DECIMAL(18, 8),
    ending_balance DECIMAL(18, 8),
    max_balance DECIMAL(18, 8),
    min_balance DECIMAL(18, 8),
    max_drawdown DECIMAL(8, 4),

    -- Risk metrics
    sharpe_ratio DECIMAL(8, 4),
    max_consecutive_wins INTEGER DEFAULT 0,
    max_consecutive_losses INTEGER DEFAULT 0,

    -- Volume metrics
    total_volume_traded DECIMAL(18, 8) DEFAULT 0,
    total_fees_paid DECIMAL(18, 8) DEFAULT 0,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Constraints
    UNIQUE(account_id, session_id, date, period_type)
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_trades_symbol_time ON trades(symbol, order_time);
CREATE INDEX IF NOT EXISTS idx_trades_session ON trades(session_id, order_time);
CREATE INDEX IF NOT EXISTS idx_balances_account_time ON account_balances(account_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_positions_symbol_status ON positions(symbol, status);
CREATE INDEX IF NOT EXISTS idx_signals_symbol_time ON trading_signals(symbol, timestamp);
CREATE INDEX IF NOT EXISTS idx_performance_date ON performance_metrics(account_id, date);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at trigger to relevant tables
CREATE TRIGGER update_accounts_updated_at BEFORE UPDATE ON accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_positions_updated_at BEFORE UPDATE ON positions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default account for testing
INSERT INTO accounts (name, exchange, account_type, is_active)
VALUES ('Default Testnet', 'binance', 'testnet', true)
ON CONFLICT (name) DO NOTHING;