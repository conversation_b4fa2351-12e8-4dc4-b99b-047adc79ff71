#!/usr/bin/env python3
"""
Test script to verify the persistence system is working
"""
import os
import json
from datetime import datetime

# Test the persistence system
def test_persistence():
    print("🧪 Testing Persistence System...")
    
    # Create test data directory
    data_dir = "bot_data"
    os.makedirs(data_dir, exist_ok=True)
    
    # Test trade history
    test_trades = [
        {
            'symbol': 'BTCUSDT',
            'side': 'BUY',
            'entry_price': 50000.0,
            'exit_price': 51000.0,
            'quantity': 0.01,
            'pnl': 10.0,
            'duration_minutes': 30,
            'reason': 'Take Profit',
            'entry_time': datetime.now().isoformat(),
            'exit_time': datetime.now().isoformat()
        },
        {
            'symbol': 'BTCUSDT',
            'side': 'SELL',
            'entry_price': 51000.0,
            'exit_price': 50500.0,
            'quantity': 0.01,
            'pnl': 5.0,
            'duration_minutes': 45,
            'reason': 'Stop Loss',
            'entry_time': datetime.now().isoformat(),
            'exit_time': datetime.now().isoformat()
        }
    ]
    
    # Test bot stats
    test_stats = {
        'daily_pnl': 15.0,
        'daily_start_balance': 10000.0,
        'last_reset_date': datetime.now().date().isoformat(),
        'win_streak': 2,
        'loss_streak': 0,
        'recent_trades': test_trades
    }
    
    # Save test data
    trades_file = os.path.join(data_dir, "trade_history.json")
    stats_file = os.path.join(data_dir, "bot_stats.json")
    
    with open(trades_file, 'w') as f:
        json.dump(test_trades, f, indent=2)
    
    with open(stats_file, 'w') as f:
        json.dump(test_stats, f, indent=2)
    
    print(f"✅ Created test files:")
    print(f"   📁 {trades_file}")
    print(f"   📁 {stats_file}")
    
    # Verify files exist and can be read
    if os.path.exists(trades_file):
        with open(trades_file, 'r') as f:
            loaded_trades = json.load(f)
        print(f"✅ Trade history loaded: {len(loaded_trades)} trades")
        
        for i, trade in enumerate(loaded_trades):
            print(f"   Trade {i+1}: {trade['side']} {trade['symbol']} P&L: ${trade['pnl']:.2f}")
    
    if os.path.exists(stats_file):
        with open(stats_file, 'r') as f:
            loaded_stats = json.load(f)
        print(f"✅ Bot stats loaded:")
        print(f"   💰 Daily P&L: ${loaded_stats['daily_pnl']:.2f}")
        print(f"   🔥 Win Streak: {loaded_stats['win_streak']}")
        print(f"   📊 Start Balance: ${loaded_stats['daily_start_balance']:.2f}")
    
    print("\n🎯 Persistence system test completed!")
    print("📝 Files created in bot_data/ directory")
    print("🔄 These files should persist when the bot restarts")

if __name__ == "__main__":
    test_persistence()
