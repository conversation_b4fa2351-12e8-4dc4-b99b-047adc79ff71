# 🗄️ Trading Bot Database Setup Guide

This guide will help you set up PostgreSQL database logging for your trading bot to track all transactions, balances, positions, and performance metrics.

## 📋 Prerequisites

1. **PostgreSQL installed and running**
   - You mentioned you already have PostgreSQL installed ✅
   - Make sure the PostgreSQL service is running

2. **Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

## 🚀 Quick Setup

### Step 1: Initialize the Database

Run the initialization script to create the database and tables:

```bash
python init_database.py
```

This script will:
- Create the `trading_bot` database (if it doesn't exist)
- Create all necessary tables with proper indexes
- Set up a default account for testing
- Test the database connection

### Step 2: Configure Database Connection (Optional)

By default, the bot connects to:
- **Host**: localhost
- **Port**: 5432
- **Database**: trading_bot
- **Username**: postgres
- **Password**: (empty)

To customize these settings, set environment variables:

```bash
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=trading_bot
export DB_USER=postgres
export DB_PASSWORD=your_password
```

### Step 3: Run Your Trading Bot

Your trading bot will now automatically log to the database! No code changes needed.

```bash
python trade-bot.py
```

## 📊 What Gets Logged

### 🔄 **Trades**
- All buy/sell orders with execution details
- Binance order IDs and responses
- Commission fees and assets
- Signal strength and confidence that triggered the trade
- Stop loss and take profit levels
- Leverage and futures information

### 💰 **Account Balances**
- USDT and BTC balances (free + locked)
- Portfolio values in both USDT and BTC
- Daily P&L tracking
- Balance snapshots at startup, periodic intervals, and shutdown

### 📈 **Positions**
- Position opening with entry details
- Real-time unrealized P&L updates
- Position closing with exit details and realized P&L
- Risk management levels (stop loss, take profit, trailing stops)

### 📡 **Trading Signals**
- All signals generated by the bot (even if not acted upon)
- Technical indicator values (RSI, MACD, Bollinger Bands)
- Signal strength, confidence, and detailed breakdown
- Win/loss streak context

### 📊 **Performance Metrics**
- Daily/weekly/monthly performance summaries
- Win rate, profit/loss ratios
- Maximum drawdown and risk metrics
- Trading volume and fee analysis

## 🔍 Database Schema Overview

### Key Tables:
- **`accounts`** - Trading account configurations
- **`trading_sessions`** - Bot session tracking with strategy settings
- **`trades`** - Complete trade execution records
- **`positions`** - Position lifecycle tracking
- **`account_balances`** - Balance snapshots over time
- **`trading_signals`** - All generated signals and market data
- **`performance_metrics`** - Aggregated performance statistics

## 📈 Querying Your Data

### Recent Trades
```sql
SELECT symbol, side, quantity, price, order_time, realized_pnl
FROM trades
ORDER BY order_time DESC
LIMIT 10;
```

### Current Open Positions
```sql
SELECT symbol, side, entry_price, quantity, unrealized_pnl, entry_time
FROM positions
WHERE status = 'OPEN'
ORDER BY entry_time DESC;
```

### Daily Performance
```sql
SELECT date, total_trades, win_rate, net_pnl, net_pnl_percentage
FROM performance_metrics
WHERE period_type = 'daily'
ORDER BY date DESC
LIMIT 30;
```

### Balance History
```sql
SELECT timestamp, portfolio_value_usdt, daily_pnl_usdt, btc_price
FROM account_balances
ORDER BY timestamp DESC
LIMIT 50;
```

## 🛠️ Troubleshooting

### Database Connection Issues
1. **Check PostgreSQL is running**:
   ```bash
   brew services list | grep postgresql  # macOS
   sudo systemctl status postgresql      # Linux
   ```

2. **Test connection manually**:
   ```bash
   psql -h localhost -U postgres -d trading_bot
   ```

3. **Check database exists**:
   ```sql
   \l  -- List all databases
   ```

### Missing Dependencies
```bash
pip install psycopg2-binary sqlalchemy
```

### Permission Issues
Make sure your PostgreSQL user has the necessary permissions:
```sql
GRANT ALL PRIVILEGES ON DATABASE trading_bot TO postgres;
```

## 🔧 Advanced Configuration

### Custom Database Configuration
Create a custom database config in your bot:

```python
from database import DatabaseConfig, TradingDatabase

config = DatabaseConfig(
    host="your-host",
    port=5432,
    database="your-db",
    username="your-user",
    password="your-password"
)

db = TradingDatabase(config)
```

### Manual Database Operations
```python
from database import TradingDatabase

db = TradingDatabase()

# Log a custom balance snapshot
db.log_balance_snapshot(
    usdt_free=1000.0,
    btc_free=0.01,
    btc_price=50000.0,
    notes="Manual snapshot"
)

# Get recent performance
performance = db.get_trading_performance(days=30)
print(f"Total trades: {performance['total_trades']}")
print(f"Win rate: {performance['winning_trades']/performance['total_trades']*100:.1f}%")
```

## 🎯 Benefits

✅ **Complete Trade History** - Never lose track of your trades again
✅ **Performance Analytics** - Detailed insights into your trading performance
✅ **Risk Management** - Track position sizes, P&L, and risk metrics
✅ **Strategy Analysis** - Compare different strategy performance
✅ **Compliance Ready** - Detailed records for tax and regulatory purposes
✅ **Data Backup** - Your trading data is safely stored in PostgreSQL

## 📞 Support

If you encounter any issues:
1. Check the logs for error messages
2. Verify PostgreSQL is running and accessible
3. Ensure all dependencies are installed
4. Test the database connection with `init_database.py`

Your trading bot now has enterprise-grade data logging! 🚀