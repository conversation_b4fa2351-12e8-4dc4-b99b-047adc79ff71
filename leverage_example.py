#!/usr/bin/env python3
"""
Leverage Trading Example for Advanced Trading Bot

This example shows how to configure and use leverage trading with the bot.
⚠️  WARNING: Leverage trading is high risk and can result in significant losses!
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import from the trade-bot.py file
import importlib.util
spec = importlib.util.spec_from_file_location("trade_bot", "trade-bot.py")
trade_bot_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(trade_bot_module)

AdvancedTradingBot = trade_bot_module.AdvancedTradingBot
TradingConfig = trade_bot_module.TradingConfig
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_leverage_config(leverage_level: int = 2, strategy: str = "conservative"):
    """
    Create a trading configuration with leverage enabled
    
    Args:
        leverage_level: Leverage multiplier (1x-125x, but start small!)
        strategy: Trading strategy ("conservative", "aggressive", "degenerate_moon_boy")
    """
    
    # ⚠️  IMPORTANT SAFETY SETTINGS ⚠️
    if leverage_level > 10:
        logger.warning(f"🚨 HIGH LEVERAGE WARNING: {leverage_level}x is very risky!")
        logger.warning("🚨 Consider starting with 2x-5x leverage for safety")
    
    config = TradingConfig(
        symbol="BTCUSDT",
        
        # Position sizing - START SMALL with leverage!
        base_quantity=0.001,  # Very small base quantity
        max_position_size=0.005,  # Small max position
        
        # Risk management - CRITICAL with leverage
        risk_per_trade=0.01,  # 1% risk per trade (lower than normal)
        max_daily_loss=0.03,  # 3% max daily loss (lower than normal)
        
        # Strategy
        strategy_type=strategy,
        
        # 🔥 LEVERAGE SETTINGS 🔥
        use_leverage=True,  # Enable leverage
        leverage=leverage_level,  # Leverage multiplier
        margin_type="ISOLATED",  # Use isolated margin for safety
        futures_mode=True,  # Must be True for leverage
        
        # Technical indicators (conservative settings)
        rsi_period=14,
        rsi_oversold=25,  # More conservative
        rsi_overbought=75,  # More conservative
        
        # Risk management (tighter with leverage)
        stop_loss_atr_multiplier=1.0,  # Tighter stop losses
        take_profit_ratio=2.0,  # Reasonable profit targets
        trailing_stop_atr_multiplier=0.8,  # Tight trailing stops
        
        # Signal thresholds (higher quality signals)
        min_signal_strength=0.7,  # Higher threshold
        min_confidence=0.8,  # Higher confidence required
    )
    
    return config

def run_conservative_leverage():
    """Run bot with conservative 2x leverage"""
    logger.info("🔥 Starting CONSERVATIVE 2x Leverage Trading")
    logger.info("📊 This uses 2x leverage with tight risk management")
    
    config = create_leverage_config(leverage_level=2, strategy="conservative")
    bot = AdvancedTradingBot(config)
    
    logger.info("⚠️  LEVERAGE TRADING ACTIVE - Monitor closely!")
    bot.run()

def run_moderate_leverage():
    """Run bot with moderate 5x leverage"""
    logger.info("🔥 Starting MODERATE 5x Leverage Trading")
    logger.info("⚠️  WARNING: 5x leverage increases both profits AND losses!")
    
    config = create_leverage_config(leverage_level=5, strategy="conservative")
    bot = AdvancedTradingBot(config)
    
    logger.info("🚨 HIGH LEVERAGE ACTIVE - EXTREME CAUTION REQUIRED!")
    bot.run()

def run_aggressive_leverage():
    """Run bot with aggressive 10x leverage - VERY RISKY!"""
    logger.warning("🚨 DANGER: Starting AGGRESSIVE 10x Leverage Trading")
    logger.warning("🚨 This is EXTREMELY RISKY and can cause rapid losses!")
    logger.warning("🚨 Only use with money you can afford to lose completely!")
    
    # Extra confirmation for high leverage
    confirmation = input("Type 'I UNDERSTAND THE RISKS' to continue: ")
    if confirmation != "I UNDERSTAND THE RISKS":
        logger.info("Leverage trading cancelled for safety")
        return
    
    config = create_leverage_config(leverage_level=10, strategy="aggressive")
    bot = AdvancedTradingBot(config)
    
    logger.warning("🚨 EXTREME LEVERAGE ACTIVE - MONITOR CONSTANTLY!")
    bot.run()

def show_leverage_info():
    """Show information about leverage trading"""
    print("""
🔥 LEVERAGE TRADING INFORMATION 🔥

What is Leverage?
- Leverage allows you to control larger positions with less capital
- 2x leverage means you control $200 worth of crypto with $100
- 10x leverage means you control $1000 worth of crypto with $100

Benefits:
✅ Amplified profits when trades go in your favor
✅ More efficient use of capital
✅ Ability to profit in both rising and falling markets

Risks:
❌ Amplified losses when trades go against you
❌ Risk of liquidation if losses exceed margin
❌ Higher volatility and emotional stress
❌ Can lose more than your initial investment

Safety Tips:
🛡️  Start with low leverage (2x-3x)
🛡️  Use isolated margin (not cross margin)
🛡️  Set tight stop losses
🛡️  Never risk more than you can afford to lose
🛡️  Monitor positions closely
🛡️  Practice on testnet first

Leverage Levels:
- 1x: No leverage (spot trading)
- 2x-3x: Conservative leverage
- 5x-10x: Moderate leverage (risky)
- 20x+: High leverage (very risky)
- 50x+: Extreme leverage (extremely risky)

⚠️  REMEMBER: Leverage amplifies BOTH profits AND losses!
""")

def main():
    """Main function with leverage options"""
    if len(sys.argv) < 2:
        print("Usage: python leverage_example.py [option]")
        print("Options:")
        print("  info        - Show leverage information")
        print("  conservative - Run with 2x leverage")
        print("  moderate    - Run with 5x leverage")
        print("  aggressive  - Run with 10x leverage (RISKY!)")
        return
    
    option = sys.argv[1].lower()
    
    if option == "info":
        show_leverage_info()
    elif option == "conservative":
        run_conservative_leverage()
    elif option == "moderate":
        run_moderate_leverage()
    elif option == "aggressive":
        run_aggressive_leverage()
    else:
        print(f"Unknown option: {option}")
        print("Use 'info', 'conservative', 'moderate', or 'aggressive'")

if __name__ == "__main__":
    main()
