#!/usr/bin/env python3
"""
Database Initialization Script for Trading Bot
Run this script to set up the PostgreSQL database and tables
"""

import os
import sys
import logging
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from database import DatabaseConfig, TradingDatabase

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_database_if_not_exists(config: DatabaseConfig):
    """Create the database if it doesn't exist"""
    try:
        # Connect to PostgreSQL server (not to specific database)
        conn = psycopg2.connect(
            host=config.host,
            port=config.port,
            user=config.username,
            password=config.password,
            database='postgres'  # Connect to default postgres database
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()

        # Check if database exists
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (config.database,))
        exists = cursor.fetchone()

        if not exists:
            logger.info(f"Creating database: {config.database}")
            cursor.execute(f'CREATE DATABASE "{config.database}"')
            logger.info(f"✅ Database '{config.database}' created successfully")
        else:
            logger.info(f"Database '{config.database}' already exists")

        cursor.close()
        conn.close()

    except Exception as e:
        logger.error(f"❌ Failed to create database: {e}")
        raise

def run_schema_file(config: DatabaseConfig, schema_file: str = "database_schema.sql"):
    """Run the schema SQL file to create tables"""
    try:
        if not os.path.exists(schema_file):
            logger.error(f"Schema file not found: {schema_file}")
            return False

        # Connect to the trading bot database
        conn = psycopg2.connect(
            host=config.host,
            port=config.port,
            user=config.username,
            password=config.password,
            database=config.database
        )
        cursor = conn.cursor()

        # Read and execute schema file
        logger.info(f"Running schema file: {schema_file}")
        with open(schema_file, 'r') as f:
            schema_sql = f.read()

        cursor.execute(schema_sql)
        conn.commit()

        logger.info("✅ Database schema created successfully")

        cursor.close()
        conn.close()
        return True

    except Exception as e:
        logger.error(f"❌ Failed to run schema file: {e}")
        return False

def test_database_connection(config: DatabaseConfig):
    """Test the database connection and basic operations"""
    try:
        logger.info("Testing database connection...")

        # Create database instance
        db = TradingDatabase(config)

        # Test basic query
        result = db.execute_query("SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'public'")
        table_count = result[0]['table_count']

        logger.info(f"✅ Database connection successful! Found {table_count} tables")

        # Test account creation
        account = db.get_or_create_account("Test Account", "binance", "testnet")
        logger.info(f"✅ Account operations working: {account['name']}")

        # Test session creation
        session = db.create_trading_session("conservative", "BTCUSDT")
        logger.info(f"✅ Session operations working: {session['id']}")

        # Test balance logging
        balance_id = db.log_balance_snapshot(
            usdt_free=1000.0,
            btc_free=0.01,
            btc_price=50000.0,
            notes="Initial test balance"
        )
        logger.info(f"✅ Balance logging working: {balance_id}")

        db.close()
        return True

    except Exception as e:
        logger.error(f"❌ Database test failed: {e}")
        return False

def main():
    """Main initialization function"""
    logger.info("🚀 Starting Trading Bot Database Initialization")

    # Get database configuration
    config = DatabaseConfig(
        host=os.getenv('DB_HOST', 'localhost'),
        port=int(os.getenv('DB_PORT', '5432')),
        database=os.getenv('DB_NAME', 'trading_bot'),
        username=os.getenv('DB_USER', 'scot'),
        password=os.getenv('DB_PASSWORD', ''),
    )

    logger.info(f"Database config: {config.username}@{config.host}:{config.port}/{config.database}")

    try:
        # Step 1: Create database if it doesn't exist
        create_database_if_not_exists(config)

        # Step 2: Run schema file to create tables
        if not run_schema_file(config):
            logger.error("Failed to create database schema")
            sys.exit(1)

        # Step 3: Test database connection and operations
        if not test_database_connection(config):
            logger.error("Database test failed")
            sys.exit(1)

        logger.info("🎉 Database initialization completed successfully!")
        logger.info("Your trading bot is now ready to log trades and balances to PostgreSQL")

        # Print next steps
        print("\n" + "="*60)
        print("NEXT STEPS:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Update your trading bot to use the database")
        print("3. Run your trading bot with database logging enabled")
        print("="*60)

    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()