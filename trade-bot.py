from binance.client import Client
import pandas as pd
import numpy as np
import time
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
import json

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# API_KEY = "673Zn8T7XYKnrsxwSI8vOd6iMcRQERSNgkvIVXAyp4Imj1bzVX6Co4EypFYfh0kB"
# API_SECRET = "U9A260IyIYoi5H8fpah74Y6oqLqEnItCapYTEeLOebmqwUXGqMIG0MJwyyozbyQW"

API_KEY = "2Q0zD7ksWslHj6AIA3V9RlPExlgEQEIG39PS80F2DAmRRObKgLsziy1kFSBoDJEl"
API_SECRET = "HBcDwdfWSVwS7hLAGJpRD9CzZKkZE5bAc1X5LbSskuoHx0WaNoTHivbdz2UkgQpY"

@dataclass
class TradingConfig:
    """Configuration for the advanced trading strategy"""
    symbol: str = "BTCUSDT"
    base_quantity: float = 0.001
    max_position_size: float = 0.01
    risk_per_trade: float = 0.02  # 2% risk per trade
    max_daily_loss: float = 0.05  # 5% max daily loss

    # Strategy type
    strategy_type: str = "conservative"  # "conservative" or "aggressive"

    # Leverage trading settings
    use_leverage: bool = False  # Enable/disable leverage trading
    leverage: int = 1  # Leverage multiplier (1x = no leverage, 2x-125x for futures)
    margin_type: str = "ISOLATED"  # "ISOLATED" or "CROSSED"
    futures_mode: bool = False  # Use futures trading instead of spot

    # Technical indicators parameters
    rsi_period: int = 14
    rsi_oversold: float = 30
    rsi_overbought: float = 70

    ema_fast: int = 12
    ema_slow: int = 26
    macd_signal: int = 9

    bb_period: int = 20
    bb_std: float = 2

    atr_period: int = 14

    # Risk management
    stop_loss_atr_multiplier: float = 2.0
    take_profit_ratio: float = 2.5  # Risk:Reward ratio
    trailing_stop_atr_multiplier: float = 1.5

    # Signal thresholds
    min_signal_strength: float = 0.5
    min_confidence: float = 0.6

    # Position sizing
    volatility_lookback: int = 20
    max_correlation_threshold: float = 0.7

@dataclass
class Position:
    """Represents a trading position"""
    symbol: str
    side: str
    entry_price: float
    quantity: float
    entry_time: datetime
    stop_loss: float
    take_profit: float
    trailing_stop: Optional[float] = None
    unrealized_pnl: float = 0.0
    leverage: int = 1  # Leverage multiplier for this position
    margin_type: str = "ISOLATED"  # Margin type for futures positions
    is_futures: bool = False  # Whether this is a futures position

class TechnicalIndicators:
    """Custom implementation of technical indicators to avoid TA-Lib dependency"""

    @staticmethod
    def sma(data: np.ndarray, period: int) -> np.ndarray:
        """Simple Moving Average"""
        return pd.Series(data).rolling(window=period).mean().values

    @staticmethod
    def ema(data: np.ndarray, period: int) -> np.ndarray:
        """Exponential Moving Average"""
        return pd.Series(data).ewm(span=period).mean().values

    @staticmethod
    def rsi(data: np.ndarray, period: int = 14) -> np.ndarray:
        """Relative Strength Index"""
        delta = pd.Series(data).diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.values

    @staticmethod
    def macd(data: np.ndarray, fast: int = 12, slow: int = 26, signal: int = 9) -> tuple:
        """MACD (Moving Average Convergence Divergence)"""
        ema_fast = TechnicalIndicators.ema(data, fast)
        ema_slow = TechnicalIndicators.ema(data, slow)
        macd_line = ema_fast - ema_slow
        signal_line = TechnicalIndicators.ema(macd_line, signal)
        histogram = macd_line - signal_line
        return macd_line, signal_line, histogram

    @staticmethod
    def bollinger_bands(data: np.ndarray, period: int = 20, std_dev: float = 2) -> tuple:
        """Bollinger Bands"""
        sma = TechnicalIndicators.sma(data, period)
        std = pd.Series(data).rolling(window=period).std().values
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        return upper, sma, lower

    @staticmethod
    def atr(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
        """Average True Range"""
        high_low = high - low
        high_close = np.abs(high - np.roll(close, 1))
        low_close = np.abs(low - np.roll(close, 1))

        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        true_range[0] = high_low[0]  # First value

        return pd.Series(true_range).rolling(window=period).mean().values

    @staticmethod
    def stochastic(high: np.ndarray, low: np.ndarray, close: np.ndarray, k_period: int = 14, d_period: int = 3) -> tuple:
        """Stochastic Oscillator"""
        lowest_low = pd.Series(low).rolling(window=k_period).min()
        highest_high = pd.Series(high).rolling(window=k_period).max()

        k_percent = 100 * ((pd.Series(close) - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_period).mean()

        return k_percent.values, d_percent.values

    @staticmethod
    def williams_r(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
        """Williams %R"""
        highest_high = pd.Series(high).rolling(window=period).max()
        lowest_low = pd.Series(low).rolling(window=period).min()

        williams = -100 * ((highest_high - pd.Series(close)) / (highest_high - lowest_low))
        return williams.values

    @staticmethod
    def adx(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
        """Average Directional Index"""
        # Calculate True Range
        tr = TechnicalIndicators.atr(high, low, close, 1)

        # Calculate Directional Movement
        dm_plus = np.where((high - np.roll(high, 1)) > (np.roll(low, 1) - low),
                          np.maximum(high - np.roll(high, 1), 0), 0)
        dm_minus = np.where((np.roll(low, 1) - low) > (high - np.roll(high, 1)),
                           np.maximum(np.roll(low, 1) - low, 0), 0)

        # Smooth the values
        tr_smooth = pd.Series(tr).rolling(window=period).mean()
        dm_plus_smooth = pd.Series(dm_plus).rolling(window=period).mean()
        dm_minus_smooth = pd.Series(dm_minus).rolling(window=period).mean()

        # Calculate DI+ and DI-
        di_plus = 100 * (dm_plus_smooth / tr_smooth)
        di_minus = 100 * (dm_minus_smooth / tr_smooth)

        # Calculate DX and ADX
        dx = 100 * np.abs(di_plus - di_minus) / (di_plus + di_minus)
        adx = pd.Series(dx).rolling(window=period).mean()

        return adx.values

class AdvancedTradingBot:
    def __init__(self, config: TradingConfig):
        self.config = config
        self.client = Client(API_KEY, API_SECRET, testnet=True)

        # Verify testnet configuration
        logger.info(f"Using Binance Testnet: {self.client.testnet}")
        logger.info(f"API Base URL: {self.client.API_URL}")

        self.positions: Dict[str, Position] = {}

        # Initialize with defaults (will be overridden by load_persistent_data if data exists)
        self.daily_pnl = 0.0
        self.daily_start_balance = 0.0
        self.last_reset_date = datetime.now().date()

        # Data storage
        self.price_data: Dict[str, pd.DataFrame] = {}
        self.indicators: Dict[str, Dict] = {}

        # Price caching to avoid multiple API calls per cycle
        self.current_prices: Dict[str, float] = {}
        self.price_cache_timestamp: Dict[str, float] = {}
        self.price_cache_duration = 5.0  # Cache prices for 5 seconds

        # Exchange info for proper order formatting
        self.exchange_info: Dict[str, Dict] = {}
        self.load_exchange_info()

        # BTC accumulation tracking
        self.initial_btc_balance = self.get_btc_balance() if config.strategy_type == "accumulate_btc" else 0
        self.btc_accumulated = 0.0

        # Performance tracking
        self.win_streak = 0
        self.loss_streak = 0
        self.recent_trades = []

        # Data persistence
        self.data_dir = "bot_data"
        self.positions_file = os.path.join(self.data_dir, "positions.json")
        self.trades_file = os.path.join(self.data_dir, "trade_history.json")
        self.stats_file = os.path.join(self.data_dir, "bot_stats.json")

        # Create data directory if it doesn't exist
        os.makedirs(self.data_dir, exist_ok=True)

        # Load existing data
        self.load_persistent_data()

        # Set daily_start_balance if not loaded from persistent data
        if self.daily_start_balance == 0.0:
            self.daily_start_balance = self.get_account_balance()
            logger.info(f"Initial account balance: {self.daily_start_balance:.4f} USDT")
        else:
            logger.info(f"Loaded account balance: {self.daily_start_balance:.4f} USDT, Daily P&L: {self.daily_pnl:.4f} USDT")

        # Sync positions with Binance on startup
        self.sync_positions_with_binance()

        # Force save on startup to ensure persistence system works
        self.save_persistent_data()

        # Apply strategy-specific configurations
        self._apply_strategy_config()

        # Initialize leverage settings if enabled
        if config.use_leverage and config.futures_mode:
            self._initialize_leverage_settings()

        logger.info(f"Advanced Trading Bot initialized with {config.strategy_type} strategy: {config}")

        if config.use_leverage:
            logger.info(f"🔥 LEVERAGE ENABLED: {config.leverage}x leverage, {config.margin_type} margin")
            if config.futures_mode:
                logger.info(f"📈 FUTURES MODE: Using futures trading with leverage")

        if config.strategy_type == "accumulate_btc":
            logger.info(f"₿ Initial BTC Balance: {self.initial_btc_balance:.8f} BTC")

    def load_exchange_info(self):
        """Load exchange info for proper order formatting"""
        try:
            exchange_info = self.client.get_exchange_info()

            for symbol_info in exchange_info['symbols']:
                symbol = symbol_info['symbol']

                # Extract important filters
                filters = {}
                for filter_info in symbol_info['filters']:
                    filter_type = filter_info['filterType']
                    filters[filter_type] = filter_info

                self.exchange_info[symbol] = {
                    'baseAsset': symbol_info['baseAsset'],
                    'quoteAsset': symbol_info['quoteAsset'],
                    'status': symbol_info['status'],
                    'baseAssetPrecision': symbol_info['baseAssetPrecision'],
                    'quotePrecision': symbol_info['quotePrecision'],
                    'filters': filters
                }

            logger.info(f"Loaded exchange info for {len(self.exchange_info)} symbols")

            # Log BTCUSDT specific info for debugging
            if 'BTCUSDT' in self.exchange_info:
                btc_info = self.exchange_info['BTCUSDT']
                logger.info(f"BTCUSDT - Base precision: {btc_info['baseAssetPrecision']}, Quote precision: {btc_info['quotePrecision']}")

                if 'LOT_SIZE' in btc_info['filters']:
                    lot_size = btc_info['filters']['LOT_SIZE']
                    logger.info(f"BTCUSDT LOT_SIZE - Min: {lot_size['minQty']}, Max: {lot_size['maxQty']}, Step: {lot_size['stepSize']}")

                if 'MIN_NOTIONAL' in btc_info['filters']:
                    min_notional = btc_info['filters']['MIN_NOTIONAL']
                    logger.info(f"BTCUSDT MIN_NOTIONAL - Min: {min_notional['minNotional']}")

        except Exception as e:
            logger.error(f"Error loading exchange info: {e}")
            # Set default values for BTCUSDT if exchange info fails
            self.exchange_info['BTCUSDT'] = {
                'baseAssetPrecision': 8,
                'quotePrecision': 2,
                'filters': {
                    'LOT_SIZE': {'minQty': '0.00001', 'maxQty': '9000', 'stepSize': '0.00001'},
                    'MIN_NOTIONAL': {'minNotional': '10.0'}
                }
            }

    def format_quantity(self, symbol: str, quantity: float) -> float:
        """Format quantity according to symbol's LOT_SIZE filter"""
        if symbol not in self.exchange_info:
            logger.warning(f"No exchange info for {symbol}, using raw quantity")
            return quantity

        filters = self.exchange_info[symbol]['filters']
        if 'LOT_SIZE' not in filters:
            return quantity

        lot_size = filters['LOT_SIZE']
        step_size = float(lot_size['stepSize'])
        min_qty = float(lot_size['minQty'])
        max_qty = float(lot_size['maxQty'])

        # Round to step size
        formatted_qty = round(quantity / step_size) * step_size

        # Ensure within bounds
        formatted_qty = max(min_qty, min(formatted_qty, max_qty))

        # Format to appropriate decimal places
        if step_size >= 1:
            return int(formatted_qty)
        else:
            # Count decimal places in step size
            decimal_places = len(str(step_size).split('.')[-1])
            return round(formatted_qty, decimal_places)

    def format_price(self, symbol: str, price: float) -> float:
        """Format price according to symbol's PRICE_FILTER"""
        if symbol not in self.exchange_info:
            return round(price, 2)  # Default to 2 decimal places

        quote_precision = self.exchange_info[symbol]['quotePrecision']
        return round(price, quote_precision)

    def validate_order_size(self, symbol: str, quantity: float, price: float) -> bool:
        """Validate order meets minimum notional requirements"""
        if symbol not in self.exchange_info:
            return True

        filters = self.exchange_info[symbol]['filters']
        if 'MIN_NOTIONAL' not in filters:
            return True

        min_notional = float(filters['MIN_NOTIONAL']['minNotional'])
        order_value = quantity * price

        if order_value < min_notional:
            logger.warning(f"Order value {order_value:.2f} below minimum notional {min_notional:.2f}")
            return False

        return True

    def _apply_strategy_config(self):
        """Apply strategy-specific configurations"""
        if self.config.strategy_type == "aggressive":
            # AGGRESSIVE - More frequent trading with tighter ranges
            self.config.min_signal_strength = 0.05  # Much lower threshold for more trades
            self.config.min_confidence = 0.05       # Much lower confidence requirement

            # More sensitive RSI levels
            self.config.rsi_oversold = 40
            self.config.rsi_overbought = 60

            # Tighter stops for more frequent trades
            self.config.stop_loss_atr_multiplier = 1.3
            self.config.take_profit_ratio = 1.8

            # More aggressive trailing stops
            self.config.trailing_stop_atr_multiplier = 0.9

            # Higher risk tolerance
            self.config.risk_per_trade = 0.025  # 2.5%

            # Auto-configure leverage for aggressive strategy
            if self.config.use_leverage and not hasattr(self, '_leverage_auto_configured'):
                self.config.leverage = 5  # 5x leverage for aggressive
                self.config.futures_mode = True
                logger.info("🔥 Auto-configured 5x leverage for AGGRESSIVE strategy")
                self._leverage_auto_configured = True

            logger.info("Applied AGGRESSIVE strategy - More frequent trading!")
        elif self.config.strategy_type == "degenerate_moon_boy":
            # 🚀🌙 DEGENERATE MOON BOY - MAXIMUM RISK, MAXIMUM REWARD! 🌙🚀
            self.config.min_signal_strength = 0.05  # Take almost any signal (FOMO mode)
            self.config.min_confidence = 0.1        # Confidence is for paper hands

            # Extreme RSI levels for maximum entries
            self.config.rsi_oversold = 45   # Buy the dip earlier
            self.config.rsi_overbought = 55 # Short the top earlier

            # Aggressive stops and massive targets
            self.config.stop_loss_atr_multiplier = 0.8   # Tight stops (cut losses fast)
            self.config.take_profit_ratio = 8.0          # 8x profit targets (TO THE MOON!)

            # Ultra-aggressive trailing
            self.config.trailing_stop_atr_multiplier = 0.4  # Lock in gains aggressively

            # YOLO risk levels
            self.config.risk_per_trade = 0.15      # 15% risk per trade (DIAMOND HANDS!)
            self.config.max_daily_loss = 0.35      # 35% max daily loss (HODL!)
            self.config.base_quantity = 0.08       # Bigger positions
            self.config.max_position_size = 0.15   # Even bigger max positions

            # Moon boy specific attributes
            self.moon_boy_multiplier = 3.0          # Triple down on strong signals
            self.fomo_threshold = 0.6               # FOMO activation level
            self.diamond_hands_mode = True          # Never sell at a loss (just kidding)
            self.ape_mode_activated = True          # Full send mode

            # Auto-configure leverage for degenerate moon boy strategy
            if self.config.use_leverage and not hasattr(self, '_leverage_auto_configured'):
                self.config.leverage = 10  # 10x leverage for maximum degeneracy
                self.config.futures_mode = True
                logger.info("🚀 Auto-configured 10x leverage for DEGENERATE MOON BOY strategy")
                self._leverage_auto_configured = True

            logger.info("🚀🌙 DEGENERATE MOON BOY ACTIVATED! 🌙🚀")
            logger.info("💎 DIAMOND HANDS MODE: Maximum risk, maximum reward!")
            logger.info("🦍 APE MODE: Going full send to the moon!")
            logger.info("⚠️  WARNING: This strategy is for degens only!")
        else:
            # CONSERVATIVE - Quality over quantity
            self.config.min_signal_strength = 0.1   # Lower threshold for more activity
            self.config.min_confidence = 0.1        # Lower confidence requirement

            # Standard RSI levels
            self.config.rsi_oversold = 35
            self.config.rsi_overbought = 65

            # Reasonable stops
            self.config.stop_loss_atr_multiplier = 1.5
            self.config.take_profit_ratio = 2.0

            # Conservative trailing
            self.config.trailing_stop_atr_multiplier = 1.0

            # Standard risk
            self.config.risk_per_trade = 0.02  # 2%

            # Auto-configure leverage for conservative strategy
            if self.config.use_leverage and not hasattr(self, '_leverage_auto_configured'):
                self.config.leverage = 2  # 2x leverage for conservative
                self.config.futures_mode = True
                logger.info("🛡️ Auto-configured 2x leverage for CONSERVATIVE strategy")
                self._leverage_auto_configured = True

            logger.info("Applied CONSERVATIVE strategy - Quality focused trading!")

    def _initialize_leverage_settings(self):
        """Initialize leverage settings for futures trading"""
        try:
            if not self.config.futures_mode:
                logger.warning("Leverage enabled but futures_mode is False. Leverage only works with futures.")
                return

            # Set leverage for the symbol
            logger.info(f"Setting leverage to {self.config.leverage}x for {self.config.symbol}")
            self.client.futures_change_leverage(
                symbol=self.config.symbol,
                leverage=self.config.leverage
            )

            # Set margin type (ISOLATED or CROSSED)
            logger.info(f"Setting margin type to {self.config.margin_type} for {self.config.symbol}")
            self.client.futures_change_margin_type(
                symbol=self.config.symbol,
                marginType=self.config.margin_type
            )

            logger.info(f"✅ Leverage settings initialized: {self.config.leverage}x {self.config.margin_type}")

        except Exception as e:
            logger.error(f"❌ Failed to initialize leverage settings: {e}")
            # Don't fail completely, just disable leverage
            self.config.use_leverage = False
            self.config.futures_mode = False
            logger.warning("Leverage disabled due to initialization failure")

    def save_persistent_data(self):
        """Save positions, trade history, and stats to files"""
        try:
            logger.debug(f"Saving persistent data to {self.data_dir}...")

            # Ensure directory exists
            os.makedirs(self.data_dir, exist_ok=True)
            # Save positions
            positions_data = {}
            for symbol, position in self.positions.items():
                positions_data[symbol] = {
                    'symbol': position.symbol,
                    'side': position.side,
                    'quantity': position.quantity,
                    'entry_price': position.entry_price,
                    'stop_loss': position.stop_loss,
                    'take_profit': position.take_profit,
                    'entry_time': position.entry_time.isoformat() if hasattr(position.entry_time, 'isoformat') else str(position.entry_time),
                    'trailing_stop': getattr(position, 'trailing_stop', None),
                    'risk_amount': getattr(position, 'risk_amount', 0)
                }

            with open(self.positions_file, 'w') as f:
                json.dump(positions_data, f, indent=2)

            # Save trade history
            trade_history_data = []
            for trade in getattr(self, 'trade_history', []):
                if isinstance(trade, dict):
                    # Ensure datetime objects are serialized
                    serialized_trade = {}
                    for key, value in trade.items():
                        if hasattr(value, 'isoformat'):  # datetime object
                            serialized_trade[key] = value.isoformat()
                        else:
                            serialized_trade[key] = value
                    trade_history_data.append(serialized_trade)
                else:
                    # Convert trade object to dict if needed
                    trade_history_data.append(asdict(trade) if hasattr(trade, '__dict__') else trade)

            with open(self.trades_file, 'w') as f:
                json.dump(trade_history_data, f, indent=2)

            # Save bot stats
            stats_data = {
                'daily_pnl': self.daily_pnl,
                'daily_start_balance': self.daily_start_balance,
                'last_reset_date': self.last_reset_date.isoformat() if hasattr(self.last_reset_date, 'isoformat') else str(self.last_reset_date),
                'win_streak': self.win_streak,
                'loss_streak': self.loss_streak,
                'recent_trades': self.recent_trades[-50:] if self.recent_trades else []  # Keep last 50
            }

            with open(self.stats_file, 'w') as f:
                json.dump(stats_data, f, indent=2)

            logger.info(f"Persistent data saved successfully - {len(getattr(self, 'trade_history', []))} trades, Daily P&L: {self.daily_pnl:.4f}, Win streak: {self.win_streak}")

        except Exception as e:
            logger.error(f"Error saving persistent data: {e}")

    def load_persistent_data(self):
        """Load positions, trade history, and stats from files"""
        try:
            # Load positions
            if os.path.exists(self.positions_file):
                with open(self.positions_file, 'r') as f:
                    positions_data = json.load(f)

                for symbol, pos_data in positions_data.items():
                    position = Position(
                        symbol=pos_data['symbol'],
                        side=pos_data['side'],
                        quantity=pos_data['quantity'],
                        entry_price=pos_data['entry_price'],
                        stop_loss=pos_data['stop_loss'],
                        take_profit=pos_data['take_profit'],
                        entry_time=datetime.fromisoformat(pos_data['entry_time']) if isinstance(pos_data['entry_time'], str) else pos_data['entry_time']
                    )
                    # Add optional attributes
                    if 'trailing_stop' in pos_data and pos_data['trailing_stop']:
                        position.trailing_stop = pos_data['trailing_stop']
                    if 'risk_amount' in pos_data:
                        position.risk_amount = pos_data['risk_amount']

                    self.positions[symbol] = position

                logger.info(f"Loaded {len(self.positions)} existing positions")

            # Load trade history
            if os.path.exists(self.trades_file):
                with open(self.trades_file, 'r') as f:
                    trade_history_data = json.load(f)

                self.trade_history = trade_history_data
                logger.info(f"Loaded {len(self.trade_history)} trade history records")
            else:
                self.trade_history = []

            # Load bot stats
            if os.path.exists(self.stats_file):
                with open(self.stats_file, 'r') as f:
                    stats_data = json.load(f)

                self.daily_pnl = stats_data.get('daily_pnl', 0.0)
                self.daily_start_balance = stats_data.get('daily_start_balance', self.get_account_balance())
                # Ensure last_reset_date is always a date object, not datetime
                if 'last_reset_date' in stats_data:
                    loaded_date = datetime.fromisoformat(stats_data['last_reset_date'])
                    self.last_reset_date = loaded_date.date() if hasattr(loaded_date, 'date') else loaded_date
                else:
                    self.last_reset_date = datetime.now().date()
                self.win_streak = stats_data.get('win_streak', 0)
                self.loss_streak = stats_data.get('loss_streak', 0)
                self.recent_trades = stats_data.get('recent_trades', [])

                logger.info(f"Loaded bot stats - Daily P&L: ${self.daily_pnl:.2f}, Win streak: {self.win_streak}, Loss streak: {self.loss_streak}")

        except Exception as e:
            logger.error(f"Error loading persistent data: {e}")
            # Initialize empty data if loading fails
            self.trade_history = []

    def sync_positions_with_binance(self):
        """Sync our position tracking with actual Binance positions"""
        try:
            # Get all open orders
            open_orders = self.client.get_open_orders(symbol=self.config.symbol)

            # Get account info to check actual balances
            account = self.client.get_account()

            # Check for BTC balance (indicates we have a position)
            btc_balance = 0.0
            for balance in account['balances']:
                if balance['asset'] == 'BTC':
                    btc_balance = float(balance['free']) + float(balance['locked'])
                    break

            # If we have BTC but no tracked position, we have a sync issue
            if btc_balance > 0.0001 and self.config.symbol not in self.positions:
                logger.warning(f"Found untracked BTC position: {btc_balance} BTC")
                # We can't easily reconstruct the position details, so log it
                logger.warning("Position tracking may be out of sync with Binance")

            # If we have tracked positions but no BTC, clear the tracking
            elif btc_balance < 0.0001 and self.config.symbol in self.positions:
                logger.warning("Tracked position exists but no BTC balance found - clearing position")
                del self.positions[self.config.symbol]
                self.save_persistent_data()

            logger.debug(f"Position sync complete - BTC balance: {btc_balance}, Tracked positions: {len(self.positions)}")

        except Exception as e:
            logger.error(f"Error syncing positions: {e}")

    def get_account_balance(self) -> float:
        """Get current AVAILABLE account balance in USDT (free balance only)"""
        try:
            if self.config.futures_mode:
                # Get futures account balance
                account = self.client.futures_account()
                for balance in account['assets']:
                    if balance['asset'] == 'USDT':
                        available = float(balance['availableBalance'])
                        wallet = float(balance['walletBalance'])
                        logger.debug(f"Futures USDT Balance - Available: {available}, Wallet: {wallet}")
                        return available
                logger.warning("No USDT balance found in futures account")
                return 0.0
            else:
                # Get spot account balance
                account = self.client.get_account()
                for balance in account['balances']:
                    if balance['asset'] == 'USDT':
                        free = float(balance['free'])
                        locked = float(balance['locked'])
                        total = free + locked
                        logger.debug(f"Spot USDT Balance - Free: {free}, Locked: {locked}, Total: {total}")
                        # Return only FREE balance for trading (locked is in open orders)
                        return free
                logger.warning("No USDT balance found in spot account")
                return 0.0
        except Exception as e:
            logger.error(f"Error getting account balance: {e}")
            return 0.0



    def get_btc_balance(self) -> float:
        """Get current BTC balance"""
        try:
            account = self.client.get_account()
            for balance in account['balances']:
                if balance['asset'] == 'BTC':
                    return float(balance['free']) + float(balance['locked'])
            return 0.0
        except Exception as e:
            logger.error(f"Error getting BTC balance: {e}")
            return 0.0

    def get_total_btc_value(self) -> float:
        """Get total portfolio value in BTC terms"""
        try:
            btc_balance = self.get_btc_balance()
            usdt_balance = self.get_account_balance()
            current_btc_price = self.get_current_price(self.config.symbol)

            if current_btc_price > 0:
                btc_from_usdt = usdt_balance / current_btc_price
                return btc_balance + btc_from_usdt
            return btc_balance
        except Exception as e:
            logger.error(f"Error calculating total BTC value: {e}")
            return 0.0

    def get_historical_data(self, symbol: str, interval: str = '5m', limit: int = 500) -> pd.DataFrame:
        """Fetch historical kline data"""
        try:
            klines = self.client.get_klines(symbol=symbol, interval=interval, limit=limit)
            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])

            # Convert to proper data types
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col])

            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)

            return df
        except Exception as e:
            logger.error(f"Error fetching historical data: {e}")
            return pd.DataFrame()



    def calculate_technical_indicators(self, df: pd.DataFrame) -> Dict:
        """Calculate comprehensive technical indicators"""
        if len(df) < max(self.config.ema_slow, self.config.bb_period, self.config.atr_period):
            return {}

        indicators = {}

        try:
            # Price data - ensure they are numeric
            high = pd.to_numeric(df['high'], errors='coerce').values
            low = pd.to_numeric(df['low'], errors='coerce').values
            close = pd.to_numeric(df['close'], errors='coerce').values
            volume = pd.to_numeric(df['volume'], errors='coerce').values

            # Check for any NaN values and handle them
            if np.any(np.isnan(high)) or np.any(np.isnan(low)) or np.any(np.isnan(close)):
                logger.warning("NaN values found in price data, skipping indicator calculation")
                return {}

            # RSI
            indicators['rsi'] = TechnicalIndicators.rsi(close, self.config.rsi_period)

            # MACD
            macd, macd_signal, macd_hist = TechnicalIndicators.macd(close,
                                                                   self.config.ema_fast,
                                                                   self.config.ema_slow,
                                                                   self.config.macd_signal)
            indicators['macd'] = macd
            indicators['macd_signal'] = macd_signal
            indicators['macd_histogram'] = macd_hist

            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = TechnicalIndicators.bollinger_bands(close,
                                                                                self.config.bb_period,
                                                                                self.config.bb_std)
            indicators['bb_upper'] = bb_upper
            indicators['bb_middle'] = bb_middle
            indicators['bb_lower'] = bb_lower

            # ATR for volatility
            indicators['atr'] = TechnicalIndicators.atr(high, low, close, self.config.atr_period)

            # EMAs
            indicators['ema_fast'] = TechnicalIndicators.ema(close, self.config.ema_fast)
            indicators['ema_slow'] = TechnicalIndicators.ema(close, self.config.ema_slow)

            # Volume indicators
            indicators['volume_sma'] = TechnicalIndicators.sma(volume, 20)

            # Stochastic
            slowk, slowd = TechnicalIndicators.stochastic(high, low, close)
            indicators['stoch_k'] = slowk
            indicators['stoch_d'] = slowd

            # Williams %R
            indicators['williams_r'] = TechnicalIndicators.williams_r(high, low, close)

            # ADX for trend strength
            indicators['adx'] = TechnicalIndicators.adx(high, low, close)

            return indicators

        except Exception as e:
            logger.error(f"Error calculating technical indicators: {e}")
            return {}

    def generate_trading_signals(self, symbol: str) -> Dict[str, float]:
        """Generate ADVANCED trading signals with adaptive intelligence"""
        if symbol not in self.indicators or not self.indicators[symbol]:
            return {'signal': 0, 'confidence': 0}



        indicators = self.indicators[symbol]
        signals = []

        # Get latest values (handle NaN values)
        def get_latest_value(indicator_array, default=0):
            if indicator_array is None or len(indicator_array) == 0:
                return default
            latest = indicator_array[-1]
            return latest if not np.isnan(latest) else default

        rsi = get_latest_value(indicators['rsi'])
        macd = get_latest_value(indicators['macd'])
        macd_signal = get_latest_value(indicators['macd_signal'])
        macd_hist = get_latest_value(indicators['macd_histogram'])
        bb_upper = get_latest_value(indicators['bb_upper'])
        bb_lower = get_latest_value(indicators['bb_lower'])

        ema_fast = get_latest_value(indicators['ema_fast'])
        ema_slow = get_latest_value(indicators['ema_slow'])
        stoch_k = get_latest_value(indicators['stoch_k'])
        stoch_d = get_latest_value(indicators['stoch_d'])
        williams_r = get_latest_value(indicators['williams_r'])
        adx = get_latest_value(indicators['adx'])

        current_price = self.get_current_price(symbol)

        # Signal 1: RSI Mean Reversion
        if rsi < self.config.rsi_oversold:
            signals.append(('rsi_oversold', 1.0, 0.8))
        elif rsi > self.config.rsi_overbought:
            signals.append(('rsi_overbought', -1.0, 0.8))

        # Signal 2: MACD Crossover
        if macd > macd_signal and macd_hist > 0:
            signals.append(('macd_bullish', 1.0, 0.7))
        elif macd < macd_signal and macd_hist < 0:
            signals.append(('macd_bearish', -1.0, 0.7))

        # Signal 3: Bollinger Bands
        if current_price <= bb_lower:
            signals.append(('bb_oversold', 1.0, 0.6))
        elif current_price >= bb_upper:
            signals.append(('bb_overbought', -1.0, 0.6))

        # Signal 4: EMA Trend
        if ema_fast > ema_slow and current_price > ema_fast:
            signals.append(('ema_bullish', 1.0, 0.5))
        elif ema_fast < ema_slow and current_price < ema_fast:
            signals.append(('ema_bearish', -1.0, 0.5))

        # Signal 5: Stochastic
        if stoch_k < 20 and stoch_d < 20 and stoch_k > stoch_d:
            signals.append(('stoch_oversold', 1.0, 0.6))
        elif stoch_k > 80 and stoch_d > 80 and stoch_k < stoch_d:
            signals.append(('stoch_overbought', -1.0, 0.6))

        # Signal 6: Williams %R
        if williams_r < -80:
            signals.append(('williams_oversold', 1.0, 0.5))
        elif williams_r > -20:
            signals.append(('williams_overbought', -1.0, 0.5))

        # Strategy-specific additional signals (simplified)
        if self.config.strategy_type == "aggressive":
            # More sensitive signals for aggressive strategy
            if rsi < 50:  # More sensitive RSI
                signals.append(('aggressive_rsi_buy', 0.3, 0.3))
            elif rsi > 50:
                signals.append(('aggressive_rsi_sell', -0.3, 0.3))

            # Volume confirmation
            volume_sma = get_latest_value(indicators['volume_sma'])
            if symbol in self.price_data and not self.price_data[symbol].empty:
                current_volume = self.price_data[symbol]['volume'].iloc[-1]
                if current_volume > volume_sma * 1.1:  # Lower volume threshold
                    if ema_fast > ema_slow:
                        signals.append(('volume_bullish', 0.4, 0.3))
                    else:
                        signals.append(('volume_bearish', -0.4, 0.3))

            # Sideways market signals - trade small price movements
            price_change_pct = ((current_price - ema_fast) / ema_fast) * 100
            if abs(price_change_pct) < 0.5:  # Sideways market
                if price_change_pct > 0.1:
                    signals.append(('sideways_bullish', 0.2, 0.2))
                elif price_change_pct < -0.1:
                    signals.append(('sideways_bearish', -0.2, 0.2))

        # Signal 7: Trend Strength (ADX)
        if self.config.strategy_type == "aggressive":
            # Lower ADX threshold for aggressive strategy
            trend_strength_multiplier = min(adx / 20, 1.8) if adx > 15 else 0.7
        else:
            # Conservative strategy
            trend_strength_multiplier = min(adx / 25, 1.5) if adx > 25 else 0.5

        # Fallback signals for when market is too quiet
        if not signals and self.config.strategy_type in ["aggressive", "degenerate_moon_boy"]:
            # Generate weak signals based on small price movements
            if rsi > 50:
                signals.append(('fallback_bullish', 0.1, 0.1))
            else:
                signals.append(('fallback_bearish', -0.1, 0.1))
            logger.debug(f"Using fallback signals for {symbol}")

        # Combine signals with weighted average
        if not signals:
            logger.debug(f"No signals generated for {symbol} - RSI: {rsi:.1f}, MACD: {macd:.3f}, Price: {current_price:.2f}")
            return {'signal': 0, 'confidence': 0, 'details': []}

        total_weight = 0
        weighted_signal = 0

        for _, signal, weight in signals:
            total_weight += weight * trend_strength_multiplier
            weighted_signal += signal * weight * trend_strength_multiplier

        final_signal = weighted_signal / total_weight if total_weight > 0 else 0
        confidence = min(abs(final_signal), 1.0)

        # Debug logging for signal generation
        signal_names = [name for name, _, _ in signals]
        logger.debug(f"Generated signals for {symbol}: {signal_names}, Final: {final_signal:.3f}, Confidence: {confidence:.3f}")



        # 🚀🌙 DEGENERATE MOON BOY SIGNAL AMPLIFICATION 🌙🚀
        if self.config.strategy_type == "degenerate_moon_boy":
            # FOMO amplification - if signal is strong, GO FULL SEND!
            if abs(final_signal) > self.fomo_threshold:
                final_signal *= self.moon_boy_multiplier  # 3x amplification!
                confidence = min(1.0, confidence * 1.5)  # Boost confidence (fake it till you make it)
                logger.info(f"🦍 APE MODE ACTIVATED! Signal amplified to {final_signal:.3f}!")

            # Diamond hands mode - never be uncertain
            if confidence < 0.3:
                confidence = 0.3  # Minimum confidence for moon boys

            # YOLO mode - if we're on a win streak, send it even harder
            if self.win_streak >= 2:
                final_signal *= 1.2  # 20% boost on win streaks
                logger.info(f"💎 DIAMOND HANDS WIN STREAK BOOST! {self.win_streak} wins in a row!")

            # Revenge trading mode - if we're losing, double down (dangerous but degen)
            if self.loss_streak >= 2:
                final_signal *= 1.1  # 10% boost (smaller because we're already hurting)
                logger.info(f"🔥 REVENGE TRADING MODE! {self.loss_streak} losses - time to get it back!")

        # Clamp final values (moon boys get higher limits)
        if self.config.strategy_type == "degenerate_moon_boy":
            final_signal = max(-3.0, min(3.0, final_signal))  # Higher signal limits for degens
        else:
            final_signal = max(-2.0, min(2.0, final_signal))
        confidence = max(0.0, min(1.0, confidence))

        return {
            'signal': final_signal,
            'confidence': confidence,
            'details': [{'name': name, 'signal': sig, 'weight': weight} for name, sig, weight in signals],
            'win_streak': self.win_streak,
            'loss_streak': self.loss_streak
        }

    def calculate_position_size(self, symbol: str, entry_price: float, stop_loss: float) -> float:
        """Calculate position size based on risk management - clean and simple"""
        account_balance = self.get_account_balance()

        if account_balance < 10.0:  # Need at least $10
            logger.warning(f"Insufficient balance: ${account_balance:.2f}")
            return 0.0

        # Calculate risk per unit
        price_risk = abs(entry_price - stop_loss)
        if price_risk == 0:
            logger.warning("Stop loss equals entry price")
            return self.config.base_quantity

        # Risk amount based on balance percentage
        risk_amount = account_balance * self.config.risk_per_trade

        # Position size based on risk
        position_size = risk_amount / price_risk

        # Apply limits
        max_position_value = account_balance * 0.1  # Max 10% of balance
        max_position_size = max_position_value / entry_price

        # Final size with bounds
        position_size = min(position_size, max_position_size, self.config.max_position_size)
        position_size = max(position_size, self.config.base_quantity)

        logger.info(f"💰 Position size: ${risk_amount:.2f} risk → {position_size:.8f} BTC")
        return round(position_size, 8)

    def calculate_stop_loss_take_profit(self, symbol: str, entry_price: float, side: str) -> Tuple[float, float]:
        """Calculate stop loss and take profit levels"""
        if symbol not in self.indicators or 'atr' not in self.indicators[symbol]:
            atr = entry_price * 0.02  # Default 2% if no ATR available
        else:
            atr = self.indicators[symbol]['atr'][-1]
            if np.isnan(atr):
                atr = entry_price * 0.02

        stop_distance = atr * self.config.stop_loss_atr_multiplier

        if side == 'BUY':
            stop_loss = entry_price - stop_distance
            take_profit = entry_price + (stop_distance * self.config.take_profit_ratio)
        else:  # SELL
            stop_loss = entry_price + stop_distance
            take_profit = entry_price - (stop_distance * self.config.take_profit_ratio)

        return round(stop_loss, 2), round(take_profit, 2)

    def get_current_price(self, symbol: str, force_refresh: bool = False) -> float:
        """Get current price for symbol with caching to avoid multiple API calls"""
        import time

        current_time = time.time()

        # Check if we have a cached price that's still valid
        if (not force_refresh and
            symbol in self.current_prices and
            symbol in self.price_cache_timestamp and
            current_time - self.price_cache_timestamp[symbol] < self.price_cache_duration):
            return self.current_prices[symbol]

        # Fetch fresh price from API
        try:
            ticker = self.client.get_symbol_ticker(symbol=symbol)
            price = float(ticker['price'])

            # Cache the price
            self.current_prices[symbol] = price
            self.price_cache_timestamp[symbol] = current_time

            return price
        except Exception as e:
            logger.error(f"Error getting current price for {symbol}: {e}")

            # Return cached price if available, otherwise 0
            return self.current_prices.get(symbol, 0.0)

    def place_order(self, symbol: str, side: str, quantity: float) -> Optional[Dict]:
        """Place a market order with proper formatting and validation"""
        try:
            # Get current price
            current_price = self.get_current_price(symbol)
            if current_price == 0:
                logger.error(f"Cannot place order: unable to get current price for {symbol}")
                return None

            # Format quantity according to exchange rules
            formatted_quantity = self.format_quantity(symbol, quantity)

            # Validate order size
            if not self.validate_order_size(symbol, formatted_quantity, current_price):
                logger.error(f"Order validation failed for {symbol}")
                return None

            logger.info(f"Placing {side} order for {symbol}: {formatted_quantity:.8f} @ ~${current_price:.2f}")

            if self.config.use_leverage:
                logger.info(f"🔥 LEVERAGE: {self.config.leverage}x")

            if self.config.futures_mode:
                # Futures trading
                available_balance = self.get_account_balance()
                order_value = formatted_quantity * current_price

                if order_value > available_balance:
                    logger.error(f"Insufficient futures balance: need {order_value:.2f}, have {available_balance:.2f}")
                    return None

                logger.info(f"Futures {side} order: {formatted_quantity:.8f} {symbol}")
                order = self.client.futures_create_order(
                    symbol=symbol,
                    side=side,
                    type='MARKET',
                    quantity=formatted_quantity
                )
            else:
                # Spot trading (original logic)
                if side == 'BUY':
                    # For BUY orders, calculate USDT amount needed
                    usdt_amount = formatted_quantity * current_price
                    usdt_amount = round(usdt_amount, 2)  # Round to 2 decimal places for USDT

                    # Check balance
                    available_balance = self.get_account_balance()
                    if usdt_amount > available_balance:
                        logger.error(f"Insufficient USDT: need {usdt_amount:.2f}, have {available_balance:.2f}")
                        return None

                    logger.info(f"Spot BUY order: {usdt_amount:.2f} USDT worth of {symbol}")
                    order = self.client.order_market_buy(symbol=symbol, quoteOrderQty=usdt_amount)

                else:  # SELL
                    logger.info(f"Spot SELL order: {formatted_quantity:.8f} {symbol}")
                    order = self.client.order_market_sell(symbol=symbol, quantity=formatted_quantity)

            # Validate and log order result
            if order and order.get('status') == 'FILLED':
                executed_qty = float(order.get('executedQty', 0))

                # Calculate average price from fills
                avg_price = current_price
                if order.get('fills'):
                    total_cost = sum(float(fill['price']) * float(fill['qty']) for fill in order['fills'])
                    total_qty = sum(float(fill['qty']) for fill in order['fills'])
                    if total_qty > 0:
                        avg_price = total_cost / total_qty

                logger.info(f"✅ Order FILLED: {side} {executed_qty:.8f} {symbol} @ ${avg_price:.2f}")
                return order
            else:
                logger.error(f"❌ Order failed or not filled: {order}")
                return None

        except Exception as e:
            error_msg = str(e).lower()
            logger.error(f"❌ Order placement failed: {e}")

            if "insufficient balance" in error_msg:
                balance = self.get_account_balance()
                logger.error(f"💰 Available USDT balance: ${balance:.2f}")

            elif "lot_size" in error_msg or "precision" in error_msg:
                logger.error(f"📏 Quantity formatting issue: {formatted_quantity:.8f}")
                if symbol in self.exchange_info:
                    lot_info = self.exchange_info[symbol]['filters'].get('LOT_SIZE', {})
                    logger.error(f"📋 LOT_SIZE rules: {lot_info}")

            elif "min_notional" in error_msg:
                order_value = formatted_quantity * current_price
                logger.error(f"💵 Order value too small: ${order_value:.2f}")

            return None

    def open_position(self, symbol: str, signal_data: Dict) -> bool:
        """Open a new position based on signal"""
        if symbol in self.positions:
            logger.info(f"Position already exists for {symbol}")
            return False

        # Check daily loss limit
        if self.daily_pnl <= -self.config.max_daily_loss * self.daily_start_balance:
            logger.warning("Daily loss limit reached, no new positions")
            return False

        current_price = self.get_current_price(symbol)
        if current_price == 0:
            return False

        side = 'BUY' if signal_data['signal'] > 0 else 'SELL'

        # Calculate stop loss and take profit
        stop_loss, take_profit = self.calculate_stop_loss_take_profit(symbol, current_price, side)

        # Calculate position size
        quantity = self.calculate_position_size(symbol, current_price, stop_loss)

        # For SELL orders, check if we have an existing position
        if side == 'SELL' and symbol not in self.positions:
            logger.warning(f"Cannot SELL {symbol}: no existing position")
            return False

        # Place the order (balance checking is handled inside place_order)
        order = self.place_order(symbol, side, quantity)
        if not order:
            logger.error(f"Failed to place {side} order for {symbol}")
            return False

        # Extract execution details
        executed_qty = float(order.get('executedQty', 0))
        if executed_qty == 0:
            logger.error(f"Order executed but no quantity filled")
            return False

        # Calculate average execution price
        avg_price = current_price
        if order.get('fills'):
            total_cost = sum(float(fill['price']) * float(fill['qty']) for fill in order['fills'])
            total_qty = sum(float(fill['qty']) for fill in order['fills'])
            if total_qty > 0:
                avg_price = total_cost / total_qty

        # Create position object with actual executed values
        position = Position(
            symbol=symbol,
            side=side,
            entry_price=avg_price,
            quantity=executed_qty,
            entry_time=datetime.now(),
            stop_loss=stop_loss,
            take_profit=take_profit,
            leverage=self.config.leverage if self.config.use_leverage else 1,
            margin_type=self.config.margin_type if self.config.use_leverage else "ISOLATED",
            is_futures=self.config.futures_mode
        )

        self.positions[symbol] = position
        logger.info(f"Opened {side} position for {symbol}: {executed_qty:.6f} @ {avg_price:.2f}")
        logger.info(f"Stop Loss: {stop_loss:.2f}, Take Profit: {take_profit:.2f}")

        position_value = executed_qty * avg_price
        if self.config.use_leverage:
            effective_value = position_value * self.config.leverage
            logger.info(f"Position value: {position_value:.2f} USDT (🔥 {effective_value:.2f} USDT with {self.config.leverage}x leverage)")
        else:
            logger.info(f"Position value: {position_value:.2f} USDT")

        # Save persistent data after opening position
        self.save_persistent_data()

        return True

    def close_position(self, symbol: str, reason: str = "Manual") -> bool:
        """Close an existing position"""
        if symbol not in self.positions:
            logger.warning(f"No position found for {symbol}")
            return False

        position = self.positions[symbol]
        opposite_side = 'SELL' if position.side == 'BUY' else 'BUY'
        current_price = self.get_current_price(symbol)

        logger.info(f"🔄 Closing {position.side} position for {symbol} ({reason})")
        logger.info(f"📊 Position: {position.quantity:.8f} @ ${position.entry_price:.2f}")
        logger.info(f"💰 Current price: ${current_price:.2f}")

        # Place closing order
        order = self.place_order(symbol, opposite_side, position.quantity)
        if not order:
            logger.error(f"❌ Failed to close position for {symbol}")
            return False

        # Calculate PnL
        current_price = self.get_current_price(symbol)
        if position.side == 'BUY':
            pnl = (current_price - position.entry_price) * position.quantity
        else:
            pnl = (position.entry_price - current_price) * position.quantity

        # Apply leverage multiplier to PnL if using leverage
        if hasattr(position, 'leverage') and position.leverage > 1:
            leveraged_pnl = pnl * position.leverage
            logger.info(f"🔥 Base PnL: ${pnl:.2f}, Leveraged PnL: ${leveraged_pnl:.2f} ({position.leverage}x)")
            pnl = leveraged_pnl

        self.daily_pnl += pnl

        # ADVANCED FEATURES: Update performance tracking
        trade_duration = (datetime.now() - position.entry_time).total_seconds() / 60  # minutes
        exit_time = datetime.now()
        trade_record = {
            'symbol': symbol,
            'side': position.side,
            'entry_price': position.entry_price,
            'exit_price': current_price,
            'quantity': position.quantity,
            'pnl': pnl,
            'duration_minutes': trade_duration,
            'reason': reason,
            'entry_time': position.entry_time.isoformat() if hasattr(position.entry_time, 'isoformat') else str(position.entry_time),
            'exit_time': exit_time.isoformat(),
            'leverage': getattr(position, 'leverage', 1),
            'margin_type': getattr(position, 'margin_type', 'ISOLATED'),
            'is_futures': getattr(position, 'is_futures', False)
        }

        # Add to recent trades (keep last 50)
        self.recent_trades.append(trade_record)
        if len(self.recent_trades) > 50:
            self.recent_trades.pop(0)

        # Add to persistent trade history
        if not hasattr(self, 'trade_history'):
            self.trade_history = []
        self.trade_history.append(trade_record)

        # Update win/loss streaks
        if pnl > 0:
            self.win_streak += 1
            self.loss_streak = 0
        else:
            self.loss_streak += 1
            self.win_streak = 0

        # Update strategy performance metrics
        self.strategy_performance['total_trades'] += 1
        if pnl > 0:
            self.strategy_performance['winning_trades'] += 1
        self.strategy_performance['total_pnl'] += pnl

        logger.info(f"Closed {position.side} position for {symbol}: {position.quantity} @ {current_price}")
        logger.info(f"PnL: {pnl:.4f} USDT, Reason: {reason}")
        logger.info(f"Win Streak: {self.win_streak}, Loss Streak: {self.loss_streak}")

        del self.positions[symbol]

        # Save persistent data after closing position
        self.save_persistent_data()

        return True

    def close_all_positions(self):
        """Close all open positions"""
        if not self.positions:
            logger.info("No positions to close")
            return

        logger.info(f"Closing {len(self.positions)} open positions...")
        positions_to_close = list(self.positions.keys())

        for symbol in positions_to_close:
            try:
                success = self.close_position(symbol, "Bot shutdown")
                if success:
                    logger.info(f"Successfully closed position for {symbol}")
                else:
                    logger.error(f"Failed to close position for {symbol}")
            except Exception as e:
                logger.error(f"Error closing position for {symbol}: {e}")

        # Final sync to ensure positions are cleared
        self.sync_positions_with_binance()

    def update_position_data(self):
        """Update position data with current prices and P&L"""
        for symbol, position in self.positions.items():
            # Use cached price (should be fresh from refresh_current_prices)
            current_price = self.get_current_price(symbol)
            if current_price == 0:
                logger.warning(f"Could not get current price for {symbol}, skipping position update")
                continue

            # Calculate unrealized P&L with consistent price
            if position.side == 'BUY':
                unrealized_pnl = (current_price - position.entry_price) * position.quantity
                price_change_pct = ((current_price - position.entry_price) / position.entry_price) * 100
            else:  # SELL
                unrealized_pnl = (position.entry_price - current_price) * position.quantity
                price_change_pct = ((position.entry_price - current_price) / position.entry_price) * 100

            # Update position object with consistent data
            position.current_price = current_price
            position.unrealized_pnl = unrealized_pnl
            position.price_change_pct = price_change_pct

            # Debug logging for position updates
            logger.debug(f"Position update for {symbol}: Price=${current_price:.2f}, P&L=${unrealized_pnl:.4f}, Change={price_change_pct:.2f}%")

    def update_trailing_stops(self):
        """Update trailing stops for all positions"""
        for symbol, position in self.positions.items():
            current_price = self.get_current_price(symbol)
            if current_price == 0:
                continue

            if symbol in self.indicators and 'atr' in self.indicators[symbol]:
                atr = self.indicators[symbol]['atr'][-1]
                if np.isnan(atr):
                    continue

                trailing_distance = atr * self.config.trailing_stop_atr_multiplier

                if position.side == 'BUY':
                    new_trailing_stop = current_price - trailing_distance
                    if position.trailing_stop is None or new_trailing_stop > position.trailing_stop:
                        position.trailing_stop = new_trailing_stop
                        logger.info(f"Updated trailing stop for {symbol}: {new_trailing_stop}")
                else:  # SELL
                    new_trailing_stop = current_price + trailing_distance
                    if position.trailing_stop is None or new_trailing_stop < position.trailing_stop:
                        position.trailing_stop = new_trailing_stop
                        logger.info(f"Updated trailing stop for {symbol}: {new_trailing_stop}")

    def check_exit_conditions(self):
        """Check if any positions should be closed"""
        positions_to_close = []

        for symbol, position in self.positions.items():
            current_price = self.get_current_price(symbol)
            if current_price == 0:
                continue

            # Check exit conditions
            logger.debug(f"Checking exit for {symbol}: {position.side} @ ${current_price:.2f}")

            # DEPLORABLE STRATEGY: Close positions quickly for maximum turnover
            if self.config.strategy_type == "deplorable":
                # Close positions after 2 minutes or any small profit/loss
                position_age = (datetime.now() - position.entry_time).total_seconds() / 60

                if position_age > 2:  # 2 minutes old
                    positions_to_close.append((symbol, "Deplorable Time Limit"))
                    continue

                # Close on any profit > 0.1% or loss > 0.2%
                if position.side == 'BUY':
                    pnl_pct = (current_price - position.entry_price) / position.entry_price
                else:
                    pnl_pct = (position.entry_price - current_price) / position.entry_price

                if pnl_pct > 0.001:  # 0.1% profit
                    positions_to_close.append((symbol, "Deplorable Quick Profit"))
                    continue
                elif pnl_pct < -0.002:  # 0.2% loss
                    positions_to_close.append((symbol, "Deplorable Quick Loss"))
                    continue

            # Check stop loss
            if position.side == 'BUY' and current_price <= position.stop_loss:
                logger.info(f"🛑 Stop Loss: {current_price:.2f} <= {position.stop_loss:.2f}")
                positions_to_close.append((symbol, "Stop Loss"))
            elif position.side == 'SELL' and current_price >= position.stop_loss:
                logger.info(f"🛑 Stop Loss: {current_price:.2f} >= {position.stop_loss:.2f}")
                positions_to_close.append((symbol, "Stop Loss"))

            # Check take profit
            elif position.side == 'BUY' and current_price >= position.take_profit:
                logger.info(f"🎯 Take Profit: {current_price:.2f} >= {position.take_profit:.2f}")
                positions_to_close.append((symbol, "Take Profit"))
            elif position.side == 'SELL' and current_price <= position.take_profit:
                logger.info(f"🎯 Take Profit: {current_price:.2f} <= {position.take_profit:.2f}")
                positions_to_close.append((symbol, "Take Profit"))

            # Check trailing stop
            elif position.trailing_stop:
                if position.side == 'BUY' and current_price <= position.trailing_stop:
                    logger.info(f"📉 Trailing Stop: {current_price:.2f} <= {position.trailing_stop:.2f}")
                    positions_to_close.append((symbol, "Trailing Stop"))
                elif position.side == 'SELL' and current_price >= position.trailing_stop:
                    logger.info(f"📈 Trailing Stop: {current_price:.2f} >= {position.trailing_stop:.2f}")
                    positions_to_close.append((symbol, "Trailing Stop"))

        # Close positions
        for symbol, reason in positions_to_close:
            self.close_position(symbol, reason)

    def reset_daily_tracking(self):
        """Reset daily tracking if new day"""
        current_date = datetime.now().date()
        if current_date > self.last_reset_date:
            self.daily_pnl = 0.0
            self.daily_start_balance = self.get_account_balance()
            self.last_reset_date = current_date
            logger.info(f"Daily tracking reset for {current_date}")

    def clear_price_cache(self):
        """Clear the price cache to force fresh API calls"""
        self.current_prices.clear()
        self.price_cache_timestamp.clear()
        logger.debug("Price cache cleared")

    def refresh_current_prices(self):
        """Refresh current prices for all symbols at the start of each cycle"""
        symbols_to_refresh = [self.config.symbol]

        # Add any symbols from open positions
        symbols_to_refresh.extend(self.positions.keys())

        # Remove duplicates
        symbols_to_refresh = list(set(symbols_to_refresh))

        logger.debug(f"Refreshing prices for {len(symbols_to_refresh)} symbols: {symbols_to_refresh}")

        for symbol in symbols_to_refresh:
            # Force refresh to get latest price
            price = self.get_current_price(symbol, force_refresh=True)
            if price > 0:
                logger.debug(f"Refreshed price for {symbol}: ${price:.2f}")
            else:
                logger.warning(f"Failed to refresh price for {symbol}")

    def update_data_and_indicators(self, symbol: str):
        """Update price data and technical indicators"""
        df = self.get_historical_data(symbol)
        if df.empty:
            logger.warning(f"No historical data received for {symbol}")
            return

        self.price_data[symbol] = df
        self.indicators[symbol] = self.calculate_technical_indicators(df)

    def run_trading_cycle(self):
        """Execute one complete trading cycle"""
        try:
            # Reset daily tracking if needed
            self.reset_daily_tracking()

            # Refresh current prices first (single API call per symbol per cycle)
            self.refresh_current_prices()

            # Update data and indicators
            self.update_data_and_indicators(self.config.symbol)

            # Update trailing stops
            self.update_trailing_stops()

            # Check exit conditions
            self.check_exit_conditions()

            # Sync positions with Binance (every 10 cycles to avoid spam)
            if hasattr(self, '_cycle_count'):
                self._cycle_count += 1
            else:
                self._cycle_count = 1

            if self._cycle_count % 10 == 0:
                self.sync_positions_with_binance()

            # Save persistent data every 20 cycles to preserve state
            if self._cycle_count % 20 == 0:
                self.save_persistent_data()

            # Generate trading signals
            signal_data = self.generate_trading_signals(self.config.symbol)

            # Log current status
            current_price = self.get_current_price(self.config.symbol)
            logger.info(f"Price: {current_price}, Signal: {signal_data['signal']:.3f}, "
                       f"Confidence: {signal_data['confidence']:.3f}, "
                       f"Daily PnL: {self.daily_pnl:.4f}")

            # Check for new position opportunities
            if (self.config.symbol not in self.positions and
                signal_data['confidence'] > self.config.min_confidence and
                abs(signal_data['signal']) > self.config.min_signal_strength):

                if self.config.strategy_type == "deplorable":
                    strategy_label = "� DEPLORABLE"
                elif self.config.strategy_type == "aggressive":
                    strategy_label = "🔥 AGGRESSIVE"

                else:
                    strategy_label = "🎯 CONSERVATIVE"

                logger.info(f"{strategy_label} signal detected: {signal_data}")
                self.open_position(self.config.symbol, signal_data)

            # Log position status
            if self.positions:
                for symbol, position in self.positions.items():
                    current_price = self.get_current_price(symbol)
                    if position.side == 'BUY':
                        unrealized_pnl = (current_price - position.entry_price) * position.quantity
                    else:
                        unrealized_pnl = (position.entry_price - current_price) * position.quantity

                    logger.info(f"Position {symbol}: {position.side} {position.quantity} @ {position.entry_price}, "
                               f"Current: {current_price}, Unrealized PnL: {unrealized_pnl:.4f}")

        except Exception as e:
            logger.error(f"Error in trading cycle: {e}")

    def run(self, max_iterations: int = None):
        """Main trading loop"""
        logger.info("Starting Advanced Trading Bot...")
        logger.info(f"Initial balance: {self.get_account_balance():.4f} USDT")

        iteration = 0
        try:
            while True:
                if max_iterations and iteration >= max_iterations:
                    logger.info(f"Reached maximum iterations: {max_iterations}")
                    break

                self.run_trading_cycle()

                iteration += 1

                # Save data every 10 iterations (roughly every 2-3 minutes)
                if iteration % 10 == 0:
                    self.save_persistent_data()

                # Strategy-based cycle times
                if self.config.strategy_type == "aggressive":
                    time.sleep(10)  # 10 seconds for aggressive
                else:
                    time.sleep(15)  # 15 seconds for conservative

        except KeyboardInterrupt:
            logger.info("Trading bot stopped by user")
        except Exception as e:
            logger.error(f"Unexpected error in main loop: {e}")
        finally:
            # Close all positions before shutting down
            logger.info("Closing all positions before shutdown...")
            self.close_all_positions()

            # Save final data before shutdown
            self.save_persistent_data()
            logger.info("Persistent data saved on shutdown")

            final_balance = self.get_account_balance()
            total_pnl = final_balance - self.daily_start_balance
            logger.info(f"Final balance: {final_balance:.4f} USDT")
            logger.info(f"Total PnL: {total_pnl:.4f} USDT")

def check_balances_and_sell_eth():
    """Check all balances and sell ETH for USDT"""
    # Create a minimal config just for balance checking
    config = TradingConfig()
    bot = AdvancedTradingBot(config)

    # Check all balances
    logger.info("=== CHECKING ALL ACCOUNT BALANCES ===")
    balances = bot.get_all_balances()

    for asset, balance_info in balances.items():
        logger.info(f"{asset}: Free={balance_info['free']:.8f}, Locked={balance_info['locked']:.8f}, Total={balance_info['total']:.8f}")

    # Check if we have ETH to sell
    if 'ETH' in balances and balances['ETH']['free'] > 0:
        eth_balance = balances['ETH']['free']
        logger.info(f"Found {eth_balance:.8f} ETH available to sell")

        # Sell all ETH for USDT
        success = bot.sell_asset_for_usdt('ETH')
        if success:
            logger.info("✅ Successfully sold ETH for USDT")

            # Check new USDT balance
            new_balances = bot.get_all_balances()
            if 'USDT' in new_balances:
                logger.info(f"New USDT balance: {new_balances['USDT']['total']:.2f}")
        else:
            logger.error("❌ Failed to sell ETH")
    else:
        logger.warning("No ETH balance found to sell")

    # Check for other assets we could sell
    sellable_assets = []
    for asset, balance_info in balances.items():
        if asset not in ['USDT', 'BTC'] and balance_info['free'] > 0:
            sellable_assets.append((asset, balance_info['free']))

    if sellable_assets:
        logger.info("Other assets available to sell:")
        for asset, amount in sellable_assets:
            logger.info(f"  {asset}: {amount:.8f}")

def main():
    """Main function to run the advanced trading bot"""
    # Create configuration
    config = TradingConfig(
        symbol="BTCUSDT",
        base_quantity=0.01,
        max_position_size=0.1,
        risk_per_trade=0.02,
        max_daily_loss=0.05,
        strategy_type="conservative",  # Default to conservative

        # Leverage settings (DISABLED by default for safety)
        use_leverage=False,  # Set to True to enable leverage
        leverage=1,  # 1x = no leverage, 2x-125x for futures
        margin_type="ISOLATED",  # "ISOLATED" or "CROSSED"
        futures_mode=False,  # Set to True to use futures trading

        # Technical indicators
        rsi_period=14,
        rsi_oversold=30,
        rsi_overbought=70,

        ema_fast=12,
        ema_slow=26,
        macd_signal=9,

        bb_period=20,
        bb_std=2,

        atr_period=14,

        # Risk management
        stop_loss_atr_multiplier=2.0,
        take_profit_ratio=2.5,
        trailing_stop_atr_multiplier=1.5,

        # Signal thresholds
        min_signal_strength=0.5,
        min_confidence=0.6,

        # Position sizing
        volatility_lookback=20,
        max_correlation_threshold=0.7
    )

    # Create and run the bot
    bot = AdvancedTradingBot(config)
    bot.run()

if __name__ == "__main__":
    import sys

    # Check if we want to sell ETH first
    if len(sys.argv) > 1 and sys.argv[1] == "sell-eth":
        check_balances_and_sell_eth()
    else:
        main()
