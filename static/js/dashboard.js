// Advanced Trading Bot Dashboard JavaScript

class TradingDashboard {
    constructor() {
        this.socket = io();
        this.isConnected = false;
        this.botRunning = false;
        this.activityLog = [];
        
        this.initializeElements();
        this.setupEventListeners();
        this.setupSocketEvents();
        this.loadInitialData();
    }
    
    initializeElements() {
        // Status elements
        this.statusDot = document.getElementById('statusDot');
        this.statusText = document.getElementById('statusText');
        
        // Control buttons
        this.startBtn = document.getElementById('startBtn');
        this.stopBtn = document.getElementById('stopBtn');
        this.configBtn = document.getElementById('configBtn');
        
        // Stats elements
        this.balanceEl = document.getElementById('balance');
        this.dailyPnlEl = document.getElementById('dailyPnl');
        this.positionCountEl = document.getElementById('positionCount');
        this.signalStrengthEl = document.getElementById('signalStrength');
        this.signalConfidenceEl = document.getElementById('signalConfidence');
        
        // Containers
        this.positionsContainer = document.getElementById('positionsContainer');
        this.signalsContainer = document.getElementById('signalsContainer');
        this.activityLogEl = document.getElementById('activityLog');
        this.tradeHistoryContainer = document.getElementById('tradeHistoryContainer');

        // Additional elements
        this.totalPositionValueEl = document.getElementById('totalPositionValue');
        this.strategyBadge = document.getElementById('strategyBadge');
        this.strategySelector = document.getElementById('strategyType');

        // Performance tracking elements
        this.winStreakEl = document.getElementById('winStreak');
        this.streakTypeEl = document.getElementById('streakType');
        
        // Modal elements
        this.configModal = document.getElementById('configModal');
        this.closeModal = document.getElementById('closeModal');
        this.saveConfig = document.getElementById('saveConfig');
        this.cancelConfig = document.getElementById('cancelConfig');
    }
    
    setupEventListeners() {
        // Control buttons
        this.startBtn.addEventListener('click', () => this.startBot());
        this.stopBtn.addEventListener('click', () => this.stopBot());
        this.configBtn.addEventListener('click', () => this.showConfigModal());
        
        // Modal controls
        this.closeModal.addEventListener('click', () => this.hideConfigModal());
        this.cancelConfig.addEventListener('click', () => this.hideConfigModal());
        this.saveConfig.addEventListener('click', () => this.saveConfiguration());
        
        // Other controls
        document.getElementById('clearLog').addEventListener('click', () => this.clearActivityLog());
        document.getElementById('refreshPositions').addEventListener('click', () => this.refreshData());
        document.getElementById('refreshTrades').addEventListener('click', () => this.loadTradeHistory());

        document.getElementById('refreshPrices').addEventListener('click', () => this.refreshPrices());

        // Strategy selector
        this.strategySelector.addEventListener('change', () => this.updateStrategyInfo());

        // Leverage controls
        this.setupLeverageControls();

        // Close modal on outside click
        this.configModal.addEventListener('click', (e) => {
            if (e.target === this.configModal) {
                this.hideConfigModal();
            }
        });
    }
    
    setupSocketEvents() {
        this.socket.on('connect', () => {
            this.isConnected = true;
            this.updateConnectionStatus();
            this.addLogEntry('Connected to trading bot server', 'success');
        });
        
        this.socket.on('disconnect', () => {
            this.isConnected = false;
            this.updateConnectionStatus();
            this.addLogEntry('Disconnected from server', 'error');
        });
        
        this.socket.on('stats_update', (data) => {
            this.updateStats(data);
        });

        this.socket.on('log_message', (data) => {
            this.addLogEntry(data.message, data.type, data.timestamp);
        });



        // Start real-time updates
        this.startRealTimeUpdates();
    }

    startRealTimeUpdates() {
        // Update stats every 5 seconds (reduced frequency for better performance)
        setInterval(() => {
            if (this.isConnected && this.botRunning) {
                this.refreshData();
            }
        }, 5000);

        // Update positions every 3 seconds for real-time P&L (reduced frequency)
        setInterval(() => {
            if (this.isConnected && this.botRunning) {
                this.updatePositionsOnly();
            }
        }, 3000);

        // Update signals every 5 seconds
        setInterval(() => {
            if (this.isConnected && this.botRunning) {
                this.fetchAndUpdateSignals();
            }
        }, 5000);
    }
    
    updateConnectionStatus() {
        if (this.isConnected) {
            this.statusDot.className = 'status-dot connected';
            this.statusText.textContent = this.botRunning ? 'Running' : 'Connected';
        } else {
            this.statusDot.className = 'status-dot';
            this.statusText.textContent = 'Disconnected';
        }
    }
    
    async loadInitialData() {
        try {
            const response = await fetch('/api/stats');
            const data = await response.json();
            this.updateStats(data);
            await this.loadTradeHistory();
        } catch (error) {
            console.error('Error loading initial data:', error);
            this.addLogEntry('Error loading initial data', 'error');
        }
    }

    async loadTradeHistory() {
        try {
            const response = await fetch('/api/trades');
            const data = await response.json();
            this.updateTradeHistory(data.trades);
            this.tradeCountEl.textContent = `${data.total_trades} trades`;
        } catch (error) {
            console.error('Error loading trade history:', error);
        }
    }
    
    async startBot() {
        try {
            this.startBtn.disabled = true;
            this.startBtn.innerHTML = '<span class="loading"></span> STARTING...';
            
            const config = this.getConfiguration();
            const response = await fetch('/api/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(config)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.botRunning = true;
                this.startBtn.disabled = true;
                this.stopBtn.disabled = false;
                this.statusDot.className = 'status-dot running';
                this.statusText.textContent = 'Running';
                this.addLogEntry('Trading bot started successfully', 'success');
            } else {
                this.addLogEntry(`Failed to start bot: ${result.message}`, 'error');
            }
        } catch (error) {
            console.error('Error starting bot:', error);
            this.addLogEntry('Error starting bot', 'error');
        } finally {
            this.startBtn.disabled = false;
            this.startBtn.innerHTML = '<span class="btn-icon">▶️</span> START BOT';
        }
    }
    
    async stopBot() {
        try {
            this.stopBtn.disabled = true;
            this.stopBtn.innerHTML = '<span class="loading"></span> STOPPING...';
            
            const response = await fetch('/api/stop', {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.botRunning = false;
                this.startBtn.disabled = false;
                this.stopBtn.disabled = true;
                this.statusDot.className = 'status-dot connected';
                this.statusText.textContent = 'Connected';
                this.addLogEntry('Trading bot stopped successfully', 'success');
            } else {
                this.addLogEntry(`Failed to stop bot: ${result.message}`, 'error');
            }
        } catch (error) {
            console.error('Error stopping bot:', error);
            this.addLogEntry('Error stopping bot', 'error');
        } finally {
            this.stopBtn.disabled = false;
            this.stopBtn.innerHTML = '<span class="btn-icon">⏹️</span> STOP BOT';
        }
    }
    
    updateStats(data) {
        // Update balance
        this.balanceEl.textContent = `$${data.balance?.toFixed(2) || '0.00'}`;

        // Update daily P&L
        const pnl = data.daily_pnl || 0;
        const pnlChangeEl = document.getElementById('pnlChange');

        this.dailyPnlEl.textContent = `$${pnl.toFixed(2)}`;
        if (pnl > 0) {
            pnlChangeEl.textContent = `+${((pnl / data.balance) * 100).toFixed(2)}%`;
            pnlChangeEl.className = 'stat-change positive';
        } else if (pnl < 0) {
            pnlChangeEl.textContent = `${((pnl / data.balance) * 100).toFixed(2)}%`;
            pnlChangeEl.className = 'stat-change negative';
        } else {
            pnlChangeEl.textContent = '0.00%';
            pnlChangeEl.className = 'stat-change';
        }
        
        // Update positions
        const positionCount = Object.keys(data.positions || {}).length;
        this.positionCountEl.textContent = positionCount.toString();
        this.updatePositions(data.positions || {});

        // Update total position value
        const totalValue = data.total_position_value || 0;
        this.totalPositionValueEl.textContent = `$${totalValue.toFixed(2)} total`;
        
        // Update signals (only update values, not rebuild)
        const signals = data.signals || {};
        this.signalStrengthEl.textContent = (signals.signal || 0).toFixed(2);
        this.signalConfidenceEl.textContent = `${((signals.confidence || 0) * 100).toFixed(0)}% confidence`;
        this.updateSignalsValues(signals);

        // Update advanced features
        if (signals.win_streak !== undefined) {
            this.winStreakEl.textContent = signals.win_streak;
            if (signals.win_streak > 0) {
                this.streakTypeEl.textContent = `🔥 ${signals.win_streak} wins`;
                this.streakTypeEl.className = 'stat-change positive';
            } else if (signals.loss_streak > 0) {
                this.winStreakEl.textContent = signals.loss_streak;
                this.streakTypeEl.textContent = `❄️ ${signals.loss_streak} losses`;
                this.streakTypeEl.className = 'stat-change negative';
            } else {
                this.streakTypeEl.textContent = 'No streak';
                this.streakTypeEl.className = 'stat-change';
            }
        }

        if (signals.market_regime) {
            this.marketRegimeEl.textContent = this.formatMarketRegime(signals.market_regime);

            const sentiment = signals.market_sentiment || 0;
            if (sentiment > 0.3) {
                this.marketSentimentEl.textContent = '📈 Bullish';
                this.marketSentimentEl.className = 'stat-change positive';
            } else if (sentiment < -0.3) {
                this.marketSentimentEl.textContent = '📉 Bearish';
                this.marketSentimentEl.className = 'stat-change negative';
            } else {
                this.marketSentimentEl.textContent = '➡️ Neutral';
                this.marketSentimentEl.className = 'stat-change';
            }
        }
        
        // Update status
        if (data.status === 'Running') {
            this.botRunning = true;
            this.startBtn.disabled = true;
            this.stopBtn.disabled = false;
            this.statusDot.className = 'status-dot running';
            this.statusText.textContent = 'Running';
        } else if (data.status === 'Stopped') {
            this.botRunning = false;
            this.startBtn.disabled = false;
            this.stopBtn.disabled = true;
            this.statusDot.className = 'status-dot connected';
            this.statusText.textContent = 'Connected';
        }

        // Update strategy badge if strategy info is available
        if (data.strategy_type) {
            this.updateStrategyBadge(data.strategy_type);
        }
    }
    
    updatePositions(positions) {
        const currentPositions = Object.keys(positions);
        const existingCards = this.positionsContainer.querySelectorAll('.position-card');
        const existingSymbols = Array.from(existingCards).map(card =>
            card.querySelector('.position-symbol')?.textContent
        );

        // If no positions, show empty state
        if (currentPositions.length === 0) {
            this.positionsContainer.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📭</div>
                    <p>No active positions</p>
                </div>
            `;
            return;
        }

        // If positions structure changed, rebuild completely
        if (JSON.stringify(currentPositions.sort()) !== JSON.stringify(existingSymbols.sort())) {
            this.rebuildPositions(positions);
            return;
        }

        // Update existing position values only
        for (const [symbol, position] of Object.entries(positions)) {
            const card = Array.from(existingCards).find(card =>
                card.querySelector('.position-symbol')?.textContent === symbol
            );

            if (card) {
                this.updatePositionCard(card, position);
            }
        }
    }

    rebuildPositions(positions) {
        let html = '';
        for (const [symbol, position] of Object.entries(positions)) {
            const pnlClass = position.unrealized_pnl >= 0 ? 'positive' : 'negative';
            const priceChangeClass = position.price_change_pct >= 0 ? 'positive' : 'negative';

            html += `
                <div class="position-card" data-symbol="${symbol}">
                    <div class="position-header">
                        <span class="position-symbol">${symbol}</span>
                        <span class="position-side ${position.side.toLowerCase()}">${position.side}</span>
                    </div>
                    <div class="position-details">
                        <div class="position-detail">
                            <span class="label">Size:</span>
                            <span class="value">${position.quantity} ($${position.position_size_usd?.toFixed(0)})</span>
                        </div>
                        <div class="position-detail">
                            <span class="label">Entry:</span>
                            <span class="value">$${position.entry_price?.toFixed(2)} @ ${position.entry_time}</span>
                        </div>
                        <div class="position-detail">
                            <span class="label">Current:</span>
                            <span class="value">$<span class="current-price">${position.current_price?.toFixed(2)}</span> (<span class="price-change ${priceChangeClass}">${position.price_change_pct?.toFixed(2)}%</span>)</span>
                        </div>
                        <div class="position-detail">
                            <span class="label">P&L:</span>
                            <span class="value pnl-value ${pnlClass}">$<span class="pnl-amount">${position.unrealized_pnl?.toFixed(2)}</span></span>
                        </div>
                        <div class="position-detail">
                            <span class="label">Stop Loss:</span>
                            <span class="value">$${position.stop_loss?.toFixed(2)} (${(position.stop_distance_pct || 0).toFixed(1)}%)</span>
                        </div>
                        <div class="position-detail">
                            <span class="label">Take Profit:</span>
                            <span class="value">$${position.take_profit?.toFixed(2)} (${(position.target_distance_pct || 0).toFixed(1)}%)</span>
                        </div>
                        ${position.trailing_stop ? `
                        <div class="position-detail">
                            <span class="label">Trailing Stop:</span>
                            <span class="value">$${position.trailing_stop?.toFixed(2)}</span>
                        </div>
                        ` : ''}
                        <div class="position-detail">
                            <span class="label">Risk:</span>
                            <span class="value">$${position.risk_amount?.toFixed(2)}</span>
                        </div>
                    </div>
                </div>
            `;
        }
        this.positionsContainer.innerHTML = html;
    }

    updatePositionCard(card, position) {
        // Update current price
        const currentPriceEl = card.querySelector('.current-price');
        if (currentPriceEl) {
            currentPriceEl.textContent = position.current_price?.toFixed(2);
        }

        // Update price change percentage and color
        const priceChangeEl = card.querySelector('.price-change');
        if (priceChangeEl) {
            priceChangeEl.textContent = `${position.price_change_pct?.toFixed(2)}%`;
            priceChangeEl.className = `price-change ${position.price_change_pct >= 0 ? 'positive' : 'negative'}`;
        }

        // Update P&L amount and color
        const pnlAmountEl = card.querySelector('.pnl-amount');
        const pnlValueEl = card.querySelector('.pnl-value');
        if (pnlAmountEl && pnlValueEl) {
            pnlAmountEl.textContent = position.unrealized_pnl?.toFixed(2);
            pnlValueEl.className = `value pnl-value ${position.unrealized_pnl >= 0 ? 'positive' : 'negative'}`;
        }
    }
    
    updateSignals(signals) {
        if (!signals.details || signals.details.length === 0) {
            if (!this.signalsContainer.querySelector('.empty-state')) {
                this.signalsContainer.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">🔍</div>
                        <p>Waiting for signals...</p>
                    </div>
                `;
            }
            return;
        }

        // Check if we need to rebuild (signal count changed)
        const existingCards = this.signalsContainer.querySelectorAll('.signal-card');
        if (existingCards.length !== signals.details.length) {
            this.rebuildSignals(signals);
            return;
        }

        // Update existing signal values only
        signals.details.forEach((signal, index) => {
            const card = existingCards[index];
            if (card) {
                this.updateSignalCard(card, signal);
            }
        });
    }

    rebuildSignals(signals) {
        let html = '';
        signals.details.forEach((signal, index) => {
            const signalClass = signal.signal > 0 ? 'bullish' : 'bearish';
            const strengthClass = Math.abs(signal.signal) > 0.7 ? 'strong' : 'weak';
            html += `
                <div class="signal-card ${signalClass}" data-signal-index="${index}">
                    <div class="signal-header">
                        <span class="signal-name">${signal.name}</span>
                        <span class="signal-strength">${signal.signal.toFixed(2)}</span>
                    </div>
                    <div class="signal-indicator">
                        <div class="signal-dot ${strengthClass}"></div>
                        <span>Weight: ${signal.weight}</span>
                    </div>
                </div>
            `;
        });
        this.signalsContainer.innerHTML = html;
    }

    updateSignalCard(card, signal) {
        // Update signal strength value
        const strengthEl = card.querySelector('.signal-strength');
        if (strengthEl) {
            strengthEl.textContent = signal.signal.toFixed(2);
        }

        // Update signal class (bullish/bearish)
        const signalClass = signal.signal > 0 ? 'bullish' : 'bearish';
        card.className = `signal-card ${signalClass}`;

        // Update strength indicator
        const dotEl = card.querySelector('.signal-dot');
        if (dotEl) {
            const strengthClass = Math.abs(signal.signal) > 0.7 ? 'strong' : 'weak';
            dotEl.className = `signal-dot ${strengthClass}`;
        }
    }

    updateSignalsValues(signals) {
        // Only update signal values if signals container already exists and has content
        if (!signals.details || signals.details.length === 0) {
            return; // Don't update if no signals
        }

        const existingCards = this.signalsContainer.querySelectorAll('.signal-card');
        if (existingCards.length === 0) {
            // No existing cards, need to build them first
            this.updateSignals(signals);
            return;
        }

        // Update existing signal values only (no HTML rebuild)
        signals.details.forEach((signal, index) => {
            const card = existingCards[index];
            if (card) {
                this.updateSignalCard(card, signal);
            }
        });
    }
    
    updateTradeHistory(trades) {
        if (!trades || trades.length === 0) {
            this.tradeHistoryContainer.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📊</div>
                    <p>No trades yet</p>
                </div>
            `;
            return;
        }

        let html = '';
        trades.slice().reverse().forEach(trade => {
            const isProfit = trade.pnl > 0;
            const profitClass = trade.pnl > 0 ? 'profit' : trade.pnl < 0 ? 'loss' : '';
            const actionClass = trade.action.toLowerCase();
            const time = new Date(trade.timestamp).toLocaleTimeString('en-US', { hour12: false });

            html += `
                <div class="trade-entry ${profitClass}">
                    <div class="trade-header">
                        <div>
                            <span class="trade-action ${actionClass}">${trade.action}</span>
                            <span class="trade-symbol">${trade.symbol}</span>
                            <span class="position-side ${trade.side.toLowerCase()}">${trade.side}</span>
                        </div>
                        <span class="trade-time">${time}</span>
                    </div>
                    <div class="trade-details">
                        ${trade.action === 'OPEN' ? `
                            <div class="trade-detail">
                                <span class="label">Price:</span>
                                <span class="value">$${trade.price?.toFixed(2)}</span>
                            </div>
                            <div class="trade-detail">
                                <span class="label">Quantity:</span>
                                <span class="value">${trade.quantity}</span>
                            </div>
                            <div class="trade-detail">
                                <span class="label">Stop Loss:</span>
                                <span class="value">$${trade.stop_loss?.toFixed(2)}</span>
                            </div>
                            <div class="trade-detail">
                                <span class="label">Take Profit:</span>
                                <span class="value">$${trade.take_profit?.toFixed(2)}</span>
                            </div>
                            <div class="trade-detail">
                                <span class="label">Signal:</span>
                                <span class="value">${trade.signal_strength?.toFixed(2)}</span>
                            </div>
                            <div class="trade-detail">
                                <span class="label">Confidence:</span>
                                <span class="value">${(trade.confidence * 100)?.toFixed(0)}%</span>
                            </div>
                        ` : `
                            <div class="trade-detail">
                                <span class="label">Entry:</span>
                                <span class="value">$${trade.entry_price?.toFixed(2)}</span>
                            </div>
                            <div class="trade-detail">
                                <span class="label">Exit:</span>
                                <span class="value">$${trade.exit_price?.toFixed(2)}</span>
                            </div>
                            <div class="trade-detail">
                                <span class="label">P&L:</span>
                                <span class="value ${isProfit ? 'profit' : 'loss'}">$${trade.pnl?.toFixed(4)}</span>
                            </div>
                            <div class="trade-detail">
                                <span class="label">Duration:</span>
                                <span class="value">${trade.duration_minutes?.toFixed(0)}m</span>
                            </div>
                            <div class="trade-detail">
                                <span class="label">Reason:</span>
                                <span class="value">${trade.reason}</span>
                            </div>
                        `}
                    </div>
                </div>
            `;
        });

        this.tradeHistoryContainer.innerHTML = html;
    }

    addLogEntry(message, type = 'info', timestamp = null) {
        const time = timestamp || new Date().toLocaleTimeString('en-US', { hour12: false });

        const entry = {
            time,
            message,
            type
        };

        this.activityLog.unshift(entry);
        if (this.activityLog.length > 100) {
            this.activityLog = this.activityLog.slice(0, 100);
        }

        this.renderActivityLog();
    }
    
    renderActivityLog() {
        let html = '';
        this.activityLog.forEach(entry => {
            html += `
                <div class="log-entry ${entry.type}">
                    <span class="log-time">${entry.time}</span>
                    <span class="log-message">${entry.message}</span>
                </div>
            `;
        });
        this.activityLogEl.innerHTML = html;
    }
    
    clearActivityLog() {
        this.activityLog = [];
        this.renderActivityLog();
    }



    async refreshPrices() {
        try {
            this.addLogEntry('🔄 Refreshing prices...', 'info');

            const response = await fetch('/api/refresh-prices', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const result = await response.json();

            if (result.success) {
                this.addLogEntry('✅ Prices refreshed successfully', 'success');
                // Refresh position data to show updated prices and P&L
                setTimeout(() => this.refreshData(), 500);
            } else {
                this.addLogEntry(`❌ ${result.message}`, 'error');
            }
        } catch (error) {
            console.error('Error refreshing prices:', error);
            this.addLogEntry('❌ Error refreshing prices', 'error');
        }
    }

    showConfigModal() {
        this.configModal.classList.add('show');
    }
    
    hideConfigModal() {
        this.configModal.classList.remove('show');
    }
    
    updateStrategyInfo() {
        const strategy = this.strategySelector.value;
        const conservativeInfo = document.getElementById('conservativeInfo');
        const aggressiveInfo = document.getElementById('aggressiveInfo');
        const moonBoyInfo = document.getElementById('degenerate_moon_boyInfo');

        // Hide all descriptions
        conservativeInfo.classList.remove('active');
        aggressiveInfo.classList.remove('active');
        if (moonBoyInfo) moonBoyInfo.classList.remove('active');

        // Show the selected one
        if (strategy === 'aggressive') {
            aggressiveInfo.classList.add('active');
        } else if (strategy === 'degenerate_moon_boy') {
            if (moonBoyInfo) moonBoyInfo.classList.add('active');
        } else {
            conservativeInfo.classList.add('active');
        }
    }

    updateStrategyBadge(strategy) {
        let icon, name, className;

        if (strategy === 'aggressive') {
            icon = '🔥';
            name = 'AGGRESSIVE';
            className = 'aggressive';
        } else if (strategy === 'degenerate_moon_boy') {
            icon = '🚀🌙';
            name = 'MOON BOY';
            className = 'moon-boy';
        } else {
            icon = '🎯';
            name = 'CONSERVATIVE';
            className = 'conservative';
        }

        this.strategyBadge.innerHTML = `
            <span class="strategy-icon">${icon}</span>
            <span class="strategy-name">${name}</span>
        `;
        this.strategyBadge.className = `strategy-badge ${className}`;
    }

    getConfiguration() {
        const strategy = this.strategySelector.value;
        this.updateStrategyBadge(strategy);

        return {
            symbol: document.getElementById('symbol').value,
            base_quantity: parseFloat(document.getElementById('baseQuantity').value),
            risk_per_trade: parseFloat(document.getElementById('riskPerTrade').value) / 100,
            max_daily_loss: parseFloat(document.getElementById('maxDailyLoss').value) / 100,
            strategy_type: strategy,
            rsi_period: parseInt(document.getElementById('rsiPeriod').value),
            rsi_oversold: parseFloat(document.getElementById('rsiOversold').value),

            // Leverage settings
            use_leverage: document.getElementById('useLeverage').checked,
            leverage: parseInt(document.getElementById('leverageSlider').value),
            margin_type: document.getElementById('marginType').value,
            futures_mode: document.getElementById('tradingMode').value === 'futures'
        };
    }
    
    saveConfiguration() {
        // Configuration is automatically used when starting the bot
        this.hideConfigModal();
        this.addLogEntry('Configuration saved', 'success');
    }

    setupLeverageControls() {
        // Get leverage control elements
        this.useLeverageCheckbox = document.getElementById('useLeverage');
        this.leverageControls = document.getElementById('leverageControls');
        this.leverageSlider = document.getElementById('leverageSlider');
        this.leverageValue = document.getElementById('leverageValue');
        this.leverageRisk = document.getElementById('leverageRisk');
        this.effectivePower = document.getElementById('effectivePower');
        this.liquidationRisk = document.getElementById('liquidationRisk');

        // Leverage toggle
        this.useLeverageCheckbox.addEventListener('change', () => {
            this.toggleLeverageControls();
        });

        // Leverage slider
        this.leverageSlider.addEventListener('input', () => {
            this.updateLeverageDisplay();
        });

        // Leverage presets
        document.querySelectorAll('.leverage-preset').forEach(btn => {
            btn.addEventListener('click', () => {
                const leverage = parseInt(btn.dataset.leverage);
                this.setLeverage(leverage);
            });
        });

        // Strategy change updates leverage recommendations
        this.strategySelector.addEventListener('change', () => {
            this.updateRecommendedLeverage();
        });

        // Initialize
        this.updateLeverageDisplay();
        this.updateRecommendedLeverage();
    }

    toggleLeverageControls() {
        const isEnabled = this.useLeverageCheckbox.checked;
        this.leverageControls.style.display = isEnabled ? 'block' : 'none';

        if (isEnabled) {
            this.addLogEntry('⚠️ Leverage trading enabled - HIGH RISK!', 'warning');
            // Auto-enable futures mode when leverage is enabled
            document.getElementById('tradingMode').value = 'futures';
        } else {
            this.addLogEntry('Leverage trading disabled', 'info');
        }
    }

    setLeverage(leverage) {
        this.leverageSlider.value = leverage;
        this.updateLeverageDisplay();

        // Update preset button states
        document.querySelectorAll('.leverage-preset').forEach(btn => {
            btn.classList.toggle('active', parseInt(btn.dataset.leverage) === leverage);
        });
    }

    updateLeverageDisplay() {
        const leverage = parseInt(this.leverageSlider.value);
        this.leverageValue.textContent = leverage;

        // Update risk indicator
        let riskLevel, riskClass;
        if (leverage === 1) {
            riskLevel = 'No Risk';
            riskClass = 'no-risk';
        } else if (leverage <= 3) {
            riskLevel = 'Low Risk';
            riskClass = 'low-risk';
        } else if (leverage <= 10) {
            riskLevel = 'Medium Risk';
            riskClass = 'medium-risk';
        } else if (leverage <= 20) {
            riskLevel = 'High Risk';
            riskClass = 'high-risk';
        } else {
            riskLevel = 'EXTREME RISK';
            riskClass = 'extreme-risk';
        }

        this.leverageRisk.textContent = riskLevel;
        this.leverageRisk.className = `leverage-risk-indicator ${riskClass}`;

        // Update effective buying power (assuming $1000 balance for demo)
        const balance = 1000; // This should be actual balance
        const effectivePower = balance * leverage;
        this.effectivePower.textContent = `$${effectivePower.toLocaleString()}`;

        // Update liquidation risk
        let liquidationLevel;
        if (leverage <= 2) {
            liquidationLevel = 'Low';
            this.liquidationRisk.className = 'low';
        } else if (leverage <= 10) {
            liquidationLevel = 'Medium';
            this.liquidationRisk.className = 'medium';
        } else {
            liquidationLevel = 'High';
            this.liquidationRisk.className = 'high';
        }
        this.liquidationRisk.textContent = liquidationLevel;
    }

    updateRecommendedLeverage() {
        const strategy = this.strategySelector.value;
        let recommendedLeverage, maxLeverage, riskLevel;

        switch (strategy) {
            case 'conservative':
                recommendedLeverage = 2;
                maxLeverage = 3;
                riskLevel = 'Low';
                break;
            case 'aggressive':
                recommendedLeverage = 5;
                maxLeverage = 10;
                riskLevel = 'Medium';
                break;
            case 'degenerate_moon_boy':
                recommendedLeverage = 10;
                maxLeverage = 20;
                riskLevel = 'EXTREME';
                break;
            default:
                recommendedLeverage = 1;
                maxLeverage = 2;
                riskLevel = 'None';
        }

        // Update leverage slider max
        this.leverageSlider.max = maxLeverage;

        // Show strategy-specific leverage info
        this.showStrategyLeverageInfo(strategy, recommendedLeverage, maxLeverage, riskLevel);

        // Auto-set recommended leverage if leverage is enabled
        if (this.useLeverageCheckbox.checked) {
            this.setLeverage(recommendedLeverage);
        }
    }

    showStrategyLeverageInfo(strategy, recommended, max, risk) {
        // Remove existing info
        const existingInfo = document.querySelector('.strategy-leverage-info');
        if (existingInfo) {
            existingInfo.remove();
        }

        // Create new info element
        const infoDiv = document.createElement('div');
        infoDiv.className = 'strategy-leverage-info';
        infoDiv.innerHTML = `
            <strong>${strategy.toUpperCase()} Strategy Leverage:</strong><br>
            Recommended: <span class="recommended-leverage">${recommended}x</span> |
            Max Safe: ${max}x |
            Risk Level: ${risk}
        `;

        // Insert after leverage controls
        this.leverageControls.appendChild(infoDiv);
    }

    async refreshData() {
        await this.loadInitialData();
        await this.loadTradeHistory();
    }

    async updatePositionsOnly() {
        try {
            const response = await fetch('/api/positions');
            if (!response.ok) {
                console.error('Positions API error:', response.status, response.statusText);
                return;
            }
            const positions = await response.json();
            console.log('Position update:', Object.keys(positions).length, 'positions');
            this.updatePositions(positions);
        } catch (error) {
            console.error('Error updating positions:', error);
        }
    }

    async fetchAndUpdateSignals() {
        try {
            const response = await fetch('/api/signals');
            const data = await response.json();

            // Update signal display
            this.signalStrengthEl.textContent = (data.signal || 0).toFixed(2);
            this.signalConfidenceEl.textContent = `${((data.confidence || 0) * 100).toFixed(0)}% confidence`;

            // Update signals container (values only, no rebuild)
            this.updateSignalsValues(data);

            // Update advanced features if available
            if (data.win_streak !== undefined) {
                this.winStreakEl.textContent = data.win_streak;
                if (data.win_streak > 0) {
                    this.streakTypeEl.textContent = `🔥 ${data.win_streak} wins`;
                    this.streakTypeEl.className = 'stat-change positive';
                } else if (data.loss_streak > 0) {
                    this.winStreakEl.textContent = data.loss_streak;
                    this.streakTypeEl.textContent = `❄️ ${data.loss_streak} losses`;
                    this.streakTypeEl.className = 'stat-change negative';
                } else {
                    this.streakTypeEl.textContent = 'No streak';
                    this.streakTypeEl.className = 'stat-change';
                }
            }


        } catch (error) {
            console.error('Error updating signals:', error);
        }
    }
}

// Initialize dashboard when DOM is loaded
let dashboard;
document.addEventListener('DOMContentLoaded', () => {
    dashboard = new TradingDashboard();
});
