/* Advanced Trading Bot - Retro Cyberpunk Theme */

:root {
    /* Cyberpunk Color Palette */
    --primary-bg: #0a0a0f;
    --secondary-bg: #1a1a2e;
    --accent-bg: #16213e;
    --card-bg: #1e1e2e;
    --border-color: #2a2a3e;
    
    /* Neon Colors */
    --neon-cyan: #00ffff;
    --neon-pink: #ff00ff;
    --neon-green: #00ff41;
    --neon-orange: #ff8c00;
    --neon-purple: #8a2be2;
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #666666;
    
    /* Status Colors */
    --success: #00ff41;
    --danger: #ff073a;
    --warning: #ffb347;
    --info: #00bfff;
    
    /* Fonts */
    --font-mono: 'JetBrains Mono', monospace;
    
    /* Shadows & Effects */
    --glow-cyan: 0 0 20px rgba(0, 255, 255, 0.3);
    --glow-pink: 0 0 20px rgba(255, 0, 255, 0.3);
    --glow-green: 0 0 20px rgba(0, 255, 65, 0.3);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-mono);
    background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
    color: var(--text-primary);
    min-height: 100vh;
    overflow-x: hidden;
}

/* Animated Background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(0, 255, 65, 0.05) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundPulse 10s ease-in-out infinite alternate;
}

@keyframes backgroundPulse {
    0% { opacity: 0.3; }
    100% { opacity: 0.7; }
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
    z-index: 1;
}

/* Header */
.header {
    background: rgba(26, 26, 46, 0.8);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
    box-shadow: var(--glow-cyan);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.strategy-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(22, 33, 62, 0.8);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 0.85rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.strategy-badge.conservative {
    border-color: var(--neon-cyan);
    color: var(--neon-cyan);
}

.strategy-badge.aggressive {
    border-color: var(--neon-orange);
    color: var(--neon-orange);
}

.strategy-badge.moon-boy {
    border-color: #ff6b35;
    color: #ff6b35;
    background: linear-gradient(45deg, rgba(255, 107, 53, 0.2), rgba(255, 215, 0, 0.1));
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.4);
    animation: moonBoyPulse 2s infinite;
    font-weight: 700;
    text-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
}

.strategy-badge.deplorable {
    border-color: var(--danger);
    color: var(--danger);
    animation: pulse 1s infinite;
}

.strategy-badge.accumulate_btc {
    border-color: #f7931a;
    color: #f7931a;
    background: linear-gradient(45deg, rgba(247, 147, 26, 0.1), rgba(247, 147, 26, 0.05));
    box-shadow: 0 0 15px rgba(247, 147, 26, 0.3);
}

/* BTC Theme Styling */
.btc-theme {
    --btc-orange: #f7931a;
    --btc-orange-light: #ffb347;
    --btc-orange-dark: #e8851c;
}

.btc-theme .stat-value {
    color: var(--btc-orange);
    text-shadow: 0 0 10px rgba(247, 147, 26, 0.5);
}

.btc-theme .stat-card {
    border-color: rgba(247, 147, 26, 0.3);
    background: linear-gradient(135deg, rgba(247, 147, 26, 0.05), rgba(247, 147, 26, 0.02));
}

.btc-theme .stat-card:hover {
    border-color: var(--btc-orange);
    box-shadow: 0 0 20px rgba(247, 147, 26, 0.3);
}

.btc-symbol {
    color: var(--btc-orange);
    font-weight: bold;
    text-shadow: 0 0 5px rgba(247, 147, 26, 0.5);
}

.strategy-icon {
    font-size: 1rem;
}

.strategy-name {
    font-family: var(--font-mono);
    letter-spacing: 1px;
}

.title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--neon-cyan);
    text-shadow: 0 0 10px var(--neon-cyan);
    display: flex;
    align-items: center;
    gap: 10px;
}

.title .icon {
    font-size: 2.5rem;
    animation: rotate 3s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.version {
    font-size: 0.8rem;
    background: var(--neon-pink);
    color: var(--primary-bg);
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    font-weight: 500;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--danger);
    box-shadow: 0 0 10px currentColor;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background: var(--success);
}

.status-dot.running {
    background: var(--neon-green);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes moonBoyPulse {
    0% {
        box-shadow: 0 0 20px rgba(255, 107, 53, 0.4);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 30px rgba(255, 107, 53, 0.8), 0 0 40px rgba(255, 215, 0, 0.4);
        transform: scale(1.05);
    }
    100% {
        box-shadow: 0 0 20px rgba(255, 107, 53, 0.4);
        transform: scale(1);
    }
}

/* Control Panel */
.control-panel {
    background: rgba(30, 30, 46, 0.9);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.panel-header h2 {
    color: var(--neon-cyan);
    font-size: 1.2rem;
    font-weight: 600;
}

.controls {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    background: linear-gradient(45deg, var(--accent-bg), var(--card-bg));
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 12px 24px;
    border-radius: 8px;
    font-family: var(--font-mono);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.btn-start {
    border-color: var(--success);
    color: var(--success);
}

.btn-start:hover:not(:disabled) {
    background: var(--success);
    color: var(--primary-bg);
    box-shadow: var(--glow-green);
}

.btn-stop {
    border-color: var(--danger);
    color: var(--danger);
}

.btn-stop:hover:not(:disabled) {
    background: var(--danger);
    color: var(--primary-bg);
    box-shadow: 0 0 20px rgba(255, 7, 58, 0.3);
}

.btn-config {
    border-color: var(--neon-orange);
    color: var(--neon-orange);
}

.btn-config:hover {
    background: var(--neon-orange);
    color: var(--primary-bg);
    box-shadow: 0 0 20px rgba(255, 140, 0, 0.3);
}

.btn-small {
    padding: 8px 12px;
    font-size: 0.8rem;
    min-width: auto;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    background: rgba(30, 30, 46, 0.9);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    border-color: var(--neon-cyan);
    box-shadow: var(--glow-cyan);
}

.stat-header h3 {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 10px;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--neon-cyan);
    margin-bottom: 5px;
    text-shadow: 0 0 10px currentColor;
}

.stat-change {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.stat-change.positive {
    color: var(--success);
}

.stat-change.negative {
    color: var(--danger);
}

/* Main Content */
.main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.panel {
    background: rgba(30, 30, 46, 0.9);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.panel-content {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.panel-content::-webkit-scrollbar {
    width: 6px;
}

.panel-content::-webkit-scrollbar-track {
    background: var(--primary-bg);
}

.panel-content::-webkit-scrollbar-thumb {
    background: var(--neon-cyan);
    border-radius: 3px;
}

.panel-controls {
    display: flex;
    gap: 10px;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-muted);
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 10px;
    opacity: 0.5;
}

/* Position Cards */
.position-card {
    background: rgba(22, 33, 62, 0.8);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.position-card:hover {
    border-color: var(--neon-cyan);
    transform: translateX(5px);
}

.position-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.position-symbol {
    font-weight: 700;
    color: var(--neon-cyan);
    font-size: 1.1rem;
}

.position-side {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
}

.position-side.buy {
    background: var(--success);
    color: var(--primary-bg);
}

.position-side.sell {
    background: var(--danger);
    color: var(--primary-bg);
}

.position-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    font-size: 0.9rem;
}

.position-detail {
    display: flex;
    justify-content: space-between;
}

.position-detail .label {
    color: var(--text-secondary);
}

.position-detail .value {
    color: var(--text-primary);
    font-weight: 500;
}

.position-detail .value.positive {
    color: var(--success);
}

.position-detail .value.negative {
    color: var(--danger);
}

/* Signal Cards */
.signal-card {
    background: rgba(22, 33, 62, 0.8);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.signal-card.bullish {
    border-left: 4px solid var(--success);
}

.signal-card.bearish {
    border-left: 4px solid var(--danger);
}

.signal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.signal-name {
    font-weight: 600;
    color: var(--neon-cyan);
}

.signal-strength {
    font-size: 0.8rem;
    padding: 2px 6px;
    border-radius: 4px;
    background: var(--accent-bg);
}

.signal-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.signal-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--text-muted);
    animation: pulse 2s infinite;
}

.signal-dot.strong {
    background: var(--success);
}

.signal-dot.weak {
    background: var(--warning);
}

/* Bottom Panels */
.bottom-panels {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.panel-info {
    color: var(--text-secondary);
    font-size: 0.8rem;
    font-weight: 500;
}

/* Trade History */
.trade-history {
    font-family: var(--font-mono);
    font-size: 0.85rem;
}

.trade-entry {
    background: rgba(22, 33, 62, 0.6);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.trade-entry:hover {
    border-color: var(--neon-cyan);
    transform: translateX(3px);
}

.trade-entry.profit {
    border-left: 4px solid var(--success);
}

.trade-entry.loss {
    border-left: 4px solid var(--danger);
}

.trade-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.trade-action {
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.75rem;
}

.trade-action.open {
    background: var(--info);
    color: var(--primary-bg);
}

.trade-action.close {
    background: var(--warning);
    color: var(--primary-bg);
}

.trade-symbol {
    color: var(--neon-cyan);
    font-weight: 600;
}

.trade-time {
    color: var(--text-muted);
    font-size: 0.75rem;
}

.trade-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    font-size: 0.8rem;
}

.trade-detail {
    display: flex;
    justify-content: space-between;
}

.trade-detail .label {
    color: var(--text-secondary);
}

.trade-detail .value {
    color: var(--text-primary);
    font-weight: 500;
}

.trade-detail .value.profit {
    color: var(--success);
}

.trade-detail .value.loss {
    color: var(--danger);
}

/* Activity Log */
.activity-panel {
    /* No longer spans full width */
}

.activity-log {
    font-family: var(--font-mono);
    font-size: 0.85rem;
    line-height: 1.6;
}

.log-entry {
    display: flex;
    gap: 15px;
    padding: 8px 0;
    border-bottom: 1px solid rgba(42, 42, 62, 0.3);
}

.log-entry:last-child {
    border-bottom: none;
}

.log-time {
    color: var(--text-muted);
    min-width: 80px;
    font-weight: 500;
}

.log-message {
    color: var(--text-secondary);
    flex: 1;
}

.log-entry.success .log-message {
    color: var(--success);
}

.log-entry.error .log-message {
    color: var(--danger);
}

.log-entry.warning .log-message {
    color: var(--warning);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--glow-cyan);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    color: var(--neon-cyan);
    font-size: 1.3rem;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: var(--danger);
    color: var(--text-primary);
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid var(--border-color);
}

/* Config Form */
.config-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.config-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.config-group label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.config-group input {
    background: var(--accent-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 10px;
    border-radius: 6px;
    font-family: var(--font-mono);
    transition: all 0.3s ease;
}

.config-group input:focus {
    outline: none;
    border-color: var(--neon-cyan);
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.2);
}

.config-group.full-width {
    grid-column: 1 / -1;
}

.strategy-selector {
    background: var(--accent-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 10px;
    border-radius: 6px;
    font-family: var(--font-mono);
    font-size: 0.9rem;
    width: 100%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.strategy-selector:focus {
    outline: none;
    border-color: var(--neon-cyan);
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.2);
}

.strategy-selector option {
    background: var(--card-bg);
    color: var(--text-primary);
    padding: 8px;
}

.strategy-info {
    margin-top: 10px;
}

.strategy-description {
    display: none;
    background: rgba(22, 33, 62, 0.6);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 12px;
    font-size: 0.85rem;
    line-height: 1.4;
    color: var(--text-secondary);
}

.strategy-description.active {
    display: block;
}

.strategy-description strong {
    color: var(--neon-cyan);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .title {
        font-size: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .main-content {
        grid-template-columns: 1fr;
    }

    .bottom-panels {
        grid-template-columns: 1fr;
    }

    .controls {
        justify-content: center;
    }

    .config-grid {
        grid-template-columns: 1fr;
    }
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-card,
.panel,
.position-card,
.signal-card {
    animation: slideIn 0.5s ease-out;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--neon-cyan);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Leverage Controls Styles */
.leverage-section {
    border: 2px solid var(--warning);
    border-radius: 8px;
    padding: 20px;
    background: linear-gradient(135deg, rgba(255, 179, 71, 0.1) 0%, rgba(255, 179, 71, 0.05) 100%);
    margin: 20px 0;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.section-header h3 {
    color: var(--warning);
    font-size: 1.2em;
    margin: 0;
}

.leverage-toggle {
    display: flex;
    align-items: center;
    gap: 10px;
}

.toggle-checkbox {
    width: 50px;
    height: 25px;
    appearance: none;
    background: var(--border-color);
    border-radius: 25px;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.toggle-checkbox:checked {
    background: var(--warning);
    box-shadow: var(--glow-cyan);
}

.toggle-checkbox::before {
    content: '';
    position: absolute;
    width: 21px;
    height: 21px;
    border-radius: 50%;
    background: white;
    top: 2px;
    left: 2px;
    transition: all 0.3s ease;
}

.toggle-checkbox:checked::before {
    transform: translateX(25px);
}

.toggle-label {
    color: var(--text-primary);
    font-weight: 500;
    cursor: pointer;
}

.leverage-warning {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    background: rgba(255, 7, 58, 0.1);
    border: 1px solid var(--danger);
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.warning-icon {
    font-size: 1.5em;
    color: var(--warning);
    flex-shrink: 0;
}

.warning-text {
    color: var(--text-secondary);
    font-size: 0.9em;
    line-height: 1.4;
}

.leverage-controls {
    border-top: 1px solid var(--border-color);
    padding-top: 20px;
    margin-top: 20px;
}

.leverage-slider-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.leverage-slider {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: var(--border-color);
    outline: none;
    appearance: none;
    cursor: pointer;
}

.leverage-slider::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--neon-cyan);
    cursor: pointer;
    box-shadow: var(--glow-cyan);
}

.leverage-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--neon-cyan);
    cursor: pointer;
    border: none;
    box-shadow: var(--glow-cyan);
}

.leverage-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
}

#leverageValue {
    font-size: 1.5em;
    font-weight: bold;
    color: var(--neon-cyan);
}

.leverage-risk-indicator {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8em;
    font-weight: bold;
    text-transform: uppercase;
}

.leverage-risk-indicator.no-risk {
    background: var(--success);
    color: var(--primary-bg);
}

.leverage-risk-indicator.low-risk {
    background: var(--info);
    color: var(--primary-bg);
}

.leverage-risk-indicator.medium-risk {
    background: var(--warning);
    color: var(--primary-bg);
}

.leverage-risk-indicator.high-risk {
    background: var(--danger);
    color: white;
}

.leverage-risk-indicator.extreme-risk {
    background: var(--neon-pink);
    color: white;
    animation: pulse 1s infinite;
}

.leverage-presets {
    display: flex;
    gap: 10px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.leverage-preset {
    padding: 8px 16px;
    border: 1px solid var(--border-color);
    background: var(--card-bg);
    color: var(--text-primary);
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9em;
    transition: all 0.3s ease;
    font-family: var(--font-mono);
}

.leverage-preset:hover {
    border-color: var(--neon-cyan);
    box-shadow: var(--glow-cyan);
    transform: translateY(-2px);
}

.leverage-preset.active {
    background: var(--neon-cyan);
    color: var(--primary-bg);
    border-color: var(--neon-cyan);
}

.leverage-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
    padding: 15px;
    background: rgba(0, 255, 255, 0.05);
    border-radius: 6px;
    border: 1px solid rgba(0, 255, 255, 0.2);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-label {
    color: var(--text-secondary);
    font-size: 0.9em;
}

#effectivePower {
    color: var(--neon-cyan);
    font-weight: bold;
}

#liquidationRisk {
    font-weight: bold;
}

#liquidationRisk.low {
    color: var(--success);
}

#liquidationRisk.medium {
    color: var(--warning);
}

#liquidationRisk.high {
    color: var(--danger);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Strategy-specific leverage indicators */
.strategy-leverage-info {
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid var(--neon-cyan);
    border-radius: 6px;
    padding: 10px;
    margin-top: 10px;
    font-size: 0.9em;
}

.strategy-leverage-info .recommended-leverage {
    color: var(--neon-cyan);
    font-weight: bold;
}


